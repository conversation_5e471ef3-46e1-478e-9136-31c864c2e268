name: Semantic Versioning

on:
  pull_request:
    types: [ labeled ]

permissions:
  contents: write

jobs:
  update-semver:
    if: contains(fromJSON('["major", "minor", "patch"]'), github.event.label.name)
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'

    - name: Install dependencies
      run: pip install semver

    - name: Get PR labels
      id: pr-labels
      uses: joerick/pr-labels-action@v1.0.9

    - name: Increment SemVer
      run: |
        python increment_version.py --labels ${{steps.pr-labels.outputs.labels}}

    - name: Commit SemVer
      uses: stefanzweifel/git-auto-commit-action@v5
      with:
        commit_message: "auto update semantic version"
