{"last_node_id": 74, "last_link_id": 81, "nodes": [{"id": 63, "type": "BizyAirGenerateLightningImage", "pos": {"0": -16.400287628173828, "1": 700.99951171875}, "size": {"0": 390.3213195800781, "1": 601.9373779296875}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [74, 75], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAirGenerateLightningImage"}, "widgets_values": ["a living room", 493871336398282, "fixed", 1024, 1024, 1.5, 1], "shape": 1}, {"id": 72, "type": "PreviewImage", "pos": {"0": 415, "1": 910}, "size": [400.96757000000025, 380.63805999999977], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 80, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}, {"id": 67, "type": "BizyAirCannyEdgePreprocessor", "pos": {"0": 414, "1": 723}, "size": {"0": 395.78131103515625, "1": 117.03739929199219}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 75, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [77, 80], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAirCannyEdgePreprocessor"}, "widgets_values": [96, 200, 512], "shape": 1}, {"id": 69, "type": "StableDiffusionXLControlNetUnionPipeline", "pos": {"0": 862, "1": 708}, "size": [416.18394000000035, 574.5855099999999], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "openpose_image", "type": "IMAGE", "link": null, "label": "openpose_image", "shape": 7}, {"name": "depth_image", "type": "IMAGE", "link": null, "label": "depth_image", "shape": 7}, {"name": "hed_pidi_scribble_ted_image", "type": "IMAGE", "link": null, "label": "hed_pidi_scribble_ted_image", "shape": 7}, {"name": "canny_lineart_anime_lineart_mlsd_image", "type": "IMAGE", "link": 77, "label": "canny_lineart_anime_lineart_mlsd_image", "shape": 7}, {"name": "normal_image", "type": "IMAGE", "link": null, "label": "normal_image", "shape": 7}, {"name": "segment_image", "type": "IMAGE", "link": null, "label": "segment_image", "shape": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [81], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "StableDiffusionXLControlNetUnionPipeline"}, "widgets_values": [1, "fixed", 20, 1, 4.9, "Stylish modern bedroom with a monochrome color palette, featuring sleek furniture and contemporary decor elements, minimalistic design, large windows with natural light, and a tranquil ambiance.", "watermark, text", 0, 0.5], "shape": 1}, {"id": 73, "type": "PreviewImage", "pos": {"0": 1334.779052734375, "1": 714}, "size": [360.9165400000004, 570.1960199999999], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 81, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}, {"id": 74, "type": "Note", "pos": {"0": 721, "1": 395}, "size": {"0": 1202.641357421875, "1": 148.23739624023438}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["这个工作流中：\n\n1. 使用 \"BizyAir Generate Photorealistic Images\" 节点生成一张地中海风格的卧室图片。\n2. 使用 BizyAir Controlnet 预处理节点，将图片变为线稿。\n3. 使用 BizyAir ControlNet Union 节点，结合提示词，生成新的装修风格的图片。"], "color": "#432", "bgcolor": "#653"}, {"id": 66, "type": "PreviewImage", "pos": {"0": 276, "1": 346}, "size": [300.5491943359375, 246], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 74, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}], "links": [[74, 63, 0, 66, 0, "IMAGE"], [75, 63, 0, 67, 0, "IMAGE"], [77, 67, 0, 69, 3, "IMAGE"], [80, 67, 0, 72, 0, "IMAGE"], [81, 69, 0, 73, 0, "IMAGE"]], "groups": [{"title": "预览图", "bounding": [1304, 623, 414, 697], "color": "#8A8", "font_size": 24, "flags": {}}, {"title": "边缘检测模型", "bounding": [398, 622, 436, 700], "color": "#a1309b", "font_size": 24, "flags": {}}, {"title": "提示词", "bounding": [-37, 622, 433, 699], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"title": "模型", "bounding": [835, 622, 467, 699], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "中间预览图", "bounding": [233, 271, 393, 332], "color": "#8A8", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.620921323059155, "offset": [237.30596999999975, -105.30269999999958]}}, "version": 0.4}