{"last_node_id": 8, "last_link_id": 10, "nodes": [{"id": 1, "type": "LoadImage", "pos": [50, 50], "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1, 2], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["image"]}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [400, 50], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [3], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [4, 5], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [6, 7], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["DreamShaper_8_pruned.safetensors"]}, {"id": 3, "type": "CLIPTextEncode", "pos": [750, 50], "size": {"0": 315, "1": 98}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 4}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [8], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cartoon character, 3D animation style, colorful, smooth, Pixar style"]}, {"id": 4, "type": "CLIPTextEncode", "pos": [1100, 50], "size": {"0": 315, "1": 98}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [9], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["realistic, photorealistic, blurry, low quality"]}, {"id": 5, "type": "VAEEncode", "pos": [50, 400], "size": {"0": 315, "1": 46}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 1}, {"name": "vae", "type": "VAE", "link": 6}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}}, {"id": 6, "type": "K<PERSON><PERSON><PERSON>", "pos": [400, 400], "size": {"0": 315, "1": 262}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 3}, {"name": "positive", "type": "CONDITIONING", "link": 8}, {"name": "negative", "type": "CONDITIONING", "link": 9}, {"name": "latent_image", "type": "LATENT", "link": 10}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [11], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 12, 6.0, "euler_a", "normal", 0.6]}, {"id": 7, "type": "VAEDecode", "pos": [750, 400], "size": {"0": 315, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 11}, {"name": "vae", "type": "VAE", "link": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 8, "type": "SaveImage", "pos": [1100, 400], "size": {"0": 315, "1": 270}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 12}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["Simple_Fast_Cartoon"]}], "links": [[1, 1, 0, 5, 0, "IMAGE"], [2, 1, 0, 8, 0, "IMAGE"], [3, 2, 0, 6, 0, "MODEL"], [4, 2, 1, 3, 0, "CLIP"], [5, 2, 1, 4, 0, "CLIP"], [6, 2, 2, 5, 1, "VAE"], [7, 2, 2, 7, 1, "VAE"], [8, 3, 0, 6, 1, "CONDITIONING"], [9, 4, 0, 6, 2, "CONDITIONING"], [10, 5, 0, 6, 3, "LATENT"], [11, 6, 0, 7, 0, "LATENT"], [12, 7, 0, 8, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}