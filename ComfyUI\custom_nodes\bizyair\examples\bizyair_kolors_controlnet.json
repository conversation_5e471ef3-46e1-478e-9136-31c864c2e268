{"last_node_id": 91, "last_link_id": 151, "nodes": [{"id": 84, "type": "BizyAir_MZ_KolorsControlNetLoader", "pos": {"0": 1540.22216796875, "1": 1303.8807373046875}, "size": {"0": 412.025390625, "1": 163.6925048828125}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "ControlNet", "type": "BIZYAIR_CONTROL_NET", "links": [141], "slot_index": 0, "shape": 3, "label": "ControlNet"}], "properties": {"Node name for S&R": "BizyAir_MZ_KolorsControlNetLoader"}, "widgets_values": ["kolors/Kolors-ControlNet-Depth.safetensors"], "shape": 1}, {"id": 70, "type": "BizyAir_MZ_KolorsUNETLoaderV2", "pos": {"0": 1540.22216796875, "1": 1107.8809814453125}, "size": {"0": 404.70489501953125, "1": 134.22109985351562}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "BIZYAIR_MODEL", "links": [125], "slot_index": 0, "shape": 3, "label": "model"}], "properties": {"Node name for S&R": "BizyAir_MZ_KolorsUNETLoaderV2"}, "widgets_values": ["kolors/Kolors.safetensors"], "shape": 1}, {"id": 73, "type": "BizyAir_KSampler", "pos": {"0": 2436.07666015625, "1": 868.810791015625}, "size": {"0": 333.6129455566406, "1": 607.9432983398438}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 125, "label": "model"}, {"name": "positive", "type": "BIZYAIR_CONDITIONING", "link": 142, "label": "positive"}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "link": 143, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": 144, "slot_index": 3, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [130], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "BizyAir_KSampler"}, "widgets_values": [1, "fixed", 20, 4.5, "euler", "ddim_uniform", 1], "shape": 1}, {"id": 75, "type": "BizyAir_VAELoader", "pos": {"0": 2808, "1": 878}, "size": {"0": 362.5466003417969, "1": 251.60716247558594}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "vae", "type": "BIZYAIR_VAE", "links": [129], "slot_index": 0, "shape": 3, "label": "vae"}], "properties": {"Node name for S&R": "BizyAir_VAELoader"}, "widgets_values": ["sdxl/sdxl_vae.safetensors"], "shape": 1}, {"id": 76, "type": "BizyAir_VAEDecode", "pos": {"0": 2811, "1": 1180}, "size": {"0": 365.5991516113281, "1": 268.70916748046875}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 130, "label": "samples"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 129, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [145], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAir_VAEDecode"}, "widgets_values": [], "shape": 1}, {"id": 86, "type": "PreviewImage", "pos": {"0": 3252, "1": 875}, "size": {"0": 322.5527648925781, "1": 578.3197021484375}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 145, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}, {"id": 71, "type": "BizyAir_ControlNetApplyAdvanced", "pos": {"0": 2002, "1": 858}, "size": {"0": 381.9786071777344, "1": 613.9796752929688}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "positive", "type": "BIZYAIR_CONDITIONING", "link": 132, "slot_index": 0, "label": "positive"}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "link": 133, "label": "negative"}, {"name": "control_net", "type": "BIZYAIR_CONTROL_NET", "link": 141, "slot_index": 2, "label": "control_net"}, {"name": "image", "type": "IMAGE", "link": 151, "slot_index": 3, "label": "image"}], "outputs": [{"name": "positive", "type": "BIZYAIR_CONDITIONING", "links": [142], "slot_index": 0, "shape": 3, "label": "positive"}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "links": [143], "slot_index": 1, "shape": 3, "label": "negative"}], "properties": {"Node name for S&R": "BizyAir_ControlNetApplyAdvanced"}, "widgets_values": [0.8, 0, 1], "shape": 1}, {"id": 89, "type": "BizyAirDepthAnythingV2Preprocessor", "pos": {"0": 1550, "1": 875}, "size": {"0": 411.6330871582031, "1": 169.95989990234375}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 149, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [150, 151], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAirDepthAnythingV2Preprocessor"}, "widgets_values": ["depth_anything_v2_vitl.pth", 1024], "shape": 1}, {"id": 80, "type": "BizyAir_MinusZoneChatGLM3TextEncode", "pos": {"0": 1064, "1": 1190}, "size": {"0": 405.53448486328125, "1": 277.0566101074219}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [132], "slot_index": 0, "shape": 3, "label": "BIZYAIR_CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_MinusZoneChatGLM3TextEncode"}, "widgets_values": ["宇宙飞船在烈火中飞向太空"], "shape": 1}, {"id": 81, "type": "BizyAir_MinusZoneChatGLM3TextEncode", "pos": {"0": 1064, "1": 876}, "size": {"0": 407.5857849121094, "1": 253.5438690185547}, "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [133], "slot_index": 0, "shape": 3, "label": "BIZYAIR_CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_MinusZoneChatGLM3TextEncode"}, "widgets_values": ["nsfw，脸部阴影，低分辨率，jpeg伪影、模糊、糟糕，黑脸，霓虹灯"], "shape": 1}, {"id": 90, "type": "BizyAirGenerateLightningImage", "pos": {"0": 1058, "1": 507}, "size": [419.0659620299964, 259.06385639700125], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [149], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAirGenerateLightningImage"}, "widgets_values": ["a hawk", 1, "fixed", 1024, 1024, 1.5, 1], "shape": 1}, {"id": 88, "type": "PreviewImage", "pos": {"0": 2039, "1": 501}, "size": {"0": 315.96197509765625, "1": 263.638671875}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 150, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}, {"id": 85, "type": "EmptyLatentImage", "pos": {"0": 2016.283935546875, "1": 1581}, "size": [366.7324679206213, 188.554823131376], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [144], "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1], "shape": 1}, {"id": 91, "type": "Note", "pos": {"0": 1030, "1": 1585}, "size": [918.844310029996, 152.36645539700066], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["ControlNet 可以更精确地控制构图。本例展示了 Kolors Depth ControlNet。\n除了 Depth Controlnet 外，你还可以在下拉菜单中，选择其它的 ControlNet。\nSDXL 的 ControlNet，也可以应用于 Kolors 模型。"], "color": "#432", "bgcolor": "#653"}], "links": [[125, 70, 0, 73, 0, "BIZYAIR_MODEL"], [129, 75, 0, 76, 1, "BIZYAIR_VAE"], [130, 73, 0, 76, 0, "LATENT"], [132, 80, 0, 71, 0, "BIZYAIR_CONDITIONING"], [133, 81, 0, 71, 1, "BIZYAIR_CONDITIONING"], [141, 84, 0, 71, 2, "BIZYAIR_CONTROL_NET"], [142, 71, 0, 73, 1, "BIZYAIR_CONDITIONING"], [143, 71, 1, 73, 2, "BIZYAIR_CONDITIONING"], [144, 85, 0, 73, 3, "LATENT"], [145, 76, 0, 86, 0, "IMAGE"], [149, 90, 0, 89, 0, "IMAGE"], [150, 89, 0, 88, 0, "IMAGE"], [151, 89, 0, 71, 3, "IMAGE"]], "groups": [{"title": "参考图提示词", "bounding": [1031, 421, 473, 360], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "预览图", "bounding": [3211, 782, 402, 712], "color": "#444", "font_size": 24, "flags": {}}, {"title": "垫图", "bounding": [2005, 1501, 394, 302], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "采样器", "bounding": [2404, 784, 376, 713], "color": "#8A8", "font_size": 24, "flags": {}}, {"title": "提示词", "bounding": [1032, 782, 465, 714], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"title": "VAE", "bounding": [2783, 783, 425, 712], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "模型", "bounding": [1498, 783, 903, 714], "color": "#a1309b", "font_size": 24, "flags": {}}, {"title": "中间预览图", "bounding": [1999, 422, 397, 362], "color": "#444", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5131581182307069, "offset": [-1097.906461829997, -175.641731497001]}, "workspace_info": {"id": "ckS1tBu-IMHBxD8SZ6K-k", "saveLock": false, "cloudID": null, "coverMediaPath": null}}, "version": 0.4}