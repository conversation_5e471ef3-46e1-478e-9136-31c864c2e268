---
name: I want a BizyAir node like ...
about: Request support for a ComfyUI node in BizyAir
title: "[FEATURE REQUEST] Support for ComfyUI node in BizyAir"
labels: enhancement, ComfyUI
assignees: ''
---

### ComfyUI Node Repository

Please provide the repository URL of the ComfyUI node you are referring to:

- Repository URL: [e.g., https://github.com/user/repo]

### Node Description and Use Case

Describe the purpose of the node and the scenarios in which it is used:

- Node Description: [e.g., This node is used for generating high-quality images from textual descriptions.]
- Use Case: [e.g., Ideal for graphic designers looking to quickly prototype designs using text inputs.]

### Workflow Details (optional)

Please provide details about the workflow, including links, screenshots, or JSON files:

- Workflow Link: [e.g., https://example.com/workflow]
- Screenshots: [If applicable, please attach screenshots here.]
- JSON File: [If applicable, please attach the JSON file or provide a link to it.]

### Additional Context (optional)

Add any other context or information about the feature request here.
