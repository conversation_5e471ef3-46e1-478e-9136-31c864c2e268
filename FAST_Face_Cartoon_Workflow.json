{"last_node_id": 10, "last_link_id": 15, "nodes": [{"id": 1, "type": "CheckpointLoaderSimple", "pos": [50, 100], "size": {"0": 315, "1": 98}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [2, 3], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [4, 5], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["dreamshaper_8.safetensors"]}, {"id": 2, "type": "LoadImage", "pos": [50, 250], "size": {"0": 315, "1": 314}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [6], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["choose file to upload", "image"]}, {"id": 3, "type": "CLIPTextEncode", "pos": [400, 100], "size": {"0": 315, "1": 76}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["same person, 3D cartoon style, Pixar animation, preserve facial features, detailed eyes, smooth skin, high quality"]}, {"id": 4, "type": "CLIPTextEncode", "pos": [400, 200], "size": {"0": 315, "1": 76}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [8], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, bad anatomy, distorted face, realistic photo, different person"]}, {"id": 5, "type": "VAEEncode", "pos": [400, 350], "size": {"0": 210, "1": 46}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 6}, {"name": "vae", "type": "VAE", "link": 4}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [9], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}}, {"id": 6, "type": "K<PERSON><PERSON><PERSON>", "pos": [750, 100], "size": {"0": 315, "1": 262}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "positive", "type": "CONDITIONING", "link": 7}, {"name": "negative", "type": "CONDITIONING", "link": 8}, {"name": "latent_image", "type": "LATENT", "link": 9}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 8, 5.0, "euler_a", "normal", 0.25]}, {"id": 7, "type": "VAEDecode", "pos": [1100, 100], "size": {"0": 210, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 10}, {"name": "vae", "type": "VAE", "link": 5}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [11], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 8, "type": "SaveImage", "pos": [1100, 200], "size": {"0": 315, "1": 270}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 11}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["Fast_Cartoon_Result"]}], "links": [[1, 1, 0, 6, 0, "MODEL"], [2, 1, 1, 3, 0, "CLIP"], [3, 1, 1, 4, 0, "CLIP"], [4, 1, 2, 5, 1, "VAE"], [5, 1, 2, 7, 1, "VAE"], [6, 2, 0, 5, 0, "IMAGE"], [7, 3, 0, 6, 1, "CONDITIONING"], [8, 4, 0, 6, 2, "CONDITIONING"], [9, 5, 0, 6, 3, "LATENT"], [10, 6, 0, 7, 0, "LATENT"], [11, 7, 0, 8, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}