{"last_node_id": 25, "last_link_id": 35, "nodes": [{"id": 1, "type": "LoadImage", "pos": [50, 100], "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1, 15], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.jpg", "image"]}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [400, 50], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [3, 4], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [5], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["DreamShaper_8_pruned.safetensors"]}, {"id": 3, "type": "CLIPTextEncode", "pos": [400, 200], "size": {"0": 400, "1": 200}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [6], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cartoon style, 3d cartoon character, pixar style, soft lighting, expressive eyes, detailed face, keep hairstyle, preserve face"]}, {"id": 4, "type": "CLIPTextEncode", "pos": [400, 450], "size": {"0": 400, "1": 200}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 4}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["low quality, bad anatomy, blurry, distorted face, scary, noise, low-res"]}, {"id": 10, "type": "IPAdapterUnifiedLoaderFaceID", "pos": [850, 50], "size": {"0": 315, "1": 78}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2}, {"name": "ipadapter", "type": "IPADAPTER", "link": null, "slot_index": 1}], "outputs": [{"name": "model", "type": "MODEL", "links": [10], "slot_index": 0}, {"name": "ipadapter", "type": "IPADAPTER", "links": [11], "slot_index": 1}], "properties": {"Node name for S&R": "IPAdapterUnifiedLoaderFaceID"}, "widgets_values": ["FACEID"]}, {"id": 11, "type": "IPAdapterFaceID", "pos": [850, 200], "size": {"0": 315, "1": 258}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 10}, {"name": "ipadapter", "type": "IPADAPTER", "link": 11}, {"name": "image", "type": "IMAGE", "link": 15}, {"name": "image_negative", "type": "IMAGE", "link": null, "slot_index": 3}, {"name": "attn_mask", "type": "MASK", "link": null, "slot_index": 4}, {"name": "clip_vision", "type": "CLIP_VISION", "link": null, "slot_index": 5}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterFaceID"}, "widgets_values": [0.8, 0.0, "linear", 0.0, 1.0, false]}, {"id": 12, "type": "K<PERSON><PERSON><PERSON>", "pos": [1200, 200], "size": {"0": 315, "1": 262}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 12}, {"name": "positive", "type": "CONDITIONING", "link": 6}, {"name": "negative", "type": "CONDITIONING", "link": 7}, {"name": "latent_image", "type": "LATENT", "link": 13}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [14], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 6, 4.0, "dpm_fast", "normal", 0.2]}, {"id": 13, "type": "VAEEncode", "pos": [850, 500], "size": {"0": 210, "1": 46}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 1}, {"name": "vae", "type": "VAE", "link": 5}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [13], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}}, {"id": 14, "type": "VAEDecode", "pos": [1550, 200], "size": {"0": 210, "1": 46}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 14}, {"name": "vae", "type": "VAE", "link": 5}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [16], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 15, "type": "SaveImage", "pos": [1550, 300], "size": {"0": 315, "1": 270}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 16}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["FAST_Cartoon"]}], "links": [[1, 1, 0, 13, 0, "IMAGE"], [2, 2, 0, 10, 0, "MODEL"], [3, 2, 1, 3, 0, "CLIP"], [4, 2, 1, 4, 0, "CLIP"], [5, 2, 2, 13, 1, "VAE"], [5, 2, 2, 14, 1, "VAE"], [6, 3, 0, 12, 1, "CONDITIONING"], [7, 4, 0, 12, 2, "CONDITIONING"], [10, 10, 0, 11, 0, "MODEL"], [11, 10, 1, 11, 1, "IPADAPTER"], [12, 11, 0, 12, 0, "MODEL"], [13, 13, 0, 12, 3, "LATENT"], [14, 12, 0, 14, 0, "LATENT"], [15, 1, 0, 11, 2, "IMAGE"], [16, 14, 0, 15, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}