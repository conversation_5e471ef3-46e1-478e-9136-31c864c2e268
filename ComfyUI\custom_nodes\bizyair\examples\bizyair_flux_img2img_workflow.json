{"last_node_id": 66, "last_link_id": 83, "nodes": [{"id": 65, "type": "LoadImage", "pos": {"0": -1715.914306640625, "1": 828.2049560546875}, "size": {"0": 324.1668701171875, "1": 667.0117797851562}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [81], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"], "shape": 1}, {"id": 37, "type": "BizyAir_CLIPTextEncode", "pos": {"0": -450.48760986328125, "1": 827.8970947265625}, "size": {"0": 450.33880615234375, "1": 670.6404418945312}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 56, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [60], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["black forest gateau cake spelling out the words \"BizyAir\", tasty, food photography, dynamic shot"], "shape": 1}, {"id": 58, "type": "BizyAir_BasicScheduler", "pos": {"0": 46.20484161376953, "1": 1331.794921875}, "size": {"0": 369.056640625, "1": 167.2618408203125}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 72, "slot_index": 0, "label": "model"}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [73], "slot_index": 0, "shape": 3, "label": "SIGMAS"}], "properties": {"Node name for S&R": "BizyAir_BasicScheduler"}, "widgets_values": ["normal", 20, 0.85], "shape": 1}, {"id": 60, "type": "BizyAir_KSamplerSelect", "pos": {"0": 47.204837799072266, "1": 1109.794921875}, "size": {"0": 367.5697326660156, "1": 169.7233428955078}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [75], "slot_index": 0, "shape": 3, "label": "SAMPLER"}], "properties": {"Node name for S&R": "BizyAir_KSamplerSelect"}, "widgets_values": ["euler"], "shape": 1}, {"id": 47, "type": "BizyAir_BasicGuider", "pos": {"0": 49.204837799072266, "1": 835.7947998046875}, "size": {"0": 344.1338195800781, "1": 199.82601928710938}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 59, "slot_index": 0, "label": "model"}, {"name": "conditioning", "type": "BIZYAIR_CONDITIONING", "link": 60, "slot_index": 1, "label": "conditioning"}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [61], "slot_index": 0, "shape": 3, "label": "GUIDER"}], "properties": {"Node name for S&R": "BizyAir_BasicGuider"}, "widgets_values": [], "shape": 1}, {"id": 50, "type": "BizyAir_SamplerCustomAdvanced", "pos": {"0": 442.2050476074219, "1": 833.7947998046875}, "size": {"0": 444.1592712402344, "1": 650.0309448242188}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 74, "label": "noise"}, {"name": "guider", "type": "GUIDER", "link": 61, "label": "guider"}, {"name": "sampler", "type": "SAMPLER", "link": 75, "slot_index": 2, "label": "sampler"}, {"name": "sigmas", "type": "SIGMAS", "link": 73, "label": "sigmas"}, {"name": "latent_image", "type": "LATENT", "link": 83, "slot_index": 4, "label": "latent_image"}], "outputs": [{"name": "output", "type": "LATENT", "links": [66], "slot_index": 0, "shape": 3, "label": "output"}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3, "label": "denoised_output"}], "properties": {"Node name for S&R": "BizyAir_SamplerCustomAdvanced"}, "widgets_values": [], "shape": 1}, {"id": 48, "type": "BizyAir_UNETLoader", "pos": {"0": -916.************, "1": 836.9998779296875}, "size": {"0": 406.73114013671875, "1": 308.4751892089844}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_MODEL", "type": "BIZYAIR_MODEL", "links": [59, 72], "slot_index": 0, "shape": 3, "label": "BIZYAIR_MODEL"}], "properties": {"Node name for S&R": "BizyAir_UNETLoader"}, "widgets_values": ["flux/flux1-dev.sft", "default"], "shape": 1}, {"id": 36, "type": "BizyAir_DualCLIPLoader", "pos": {"0": -913.************, "1": 1189}, "size": {"0": 421.26959228515625, "1": 307.2442626953125}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_CLIP", "type": "BIZYAIR_CLIP", "links": [56], "slot_index": 0, "shape": 3, "label": "BIZYAIR_CLIP"}], "properties": {"Node name for S&R": "BizyAir_DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux"], "shape": 1}, {"id": 56, "type": "PreviewImage", "pos": {"0": 1274.923583984375, "1": 828.7943115234375}, "size": {"0": 412.41937255859375, "1": 674.7813110351562}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 68, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}, {"id": 54, "type": "BizyAir_VAEDecode", "pos": {"0": 929.5133056640625, "1": 831.794921875}, "size": {"0": 283.5361328125, "1": 663.0769653320312}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 66, "slot_index": 0, "label": "samples"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 67, "slot_index": 1, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [68], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAir_VAEDecode"}, "widgets_values": [], "shape": 1}, {"id": 55, "type": "BizyAir_VAELoader", "pos": {"0": -1367, "1": 826}, "size": {"0": 399.1780090332031, "1": 302.6155700683594}, "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "vae", "type": "BIZYAIR_VAE", "links": [67, 82], "slot_index": 0, "shape": 3, "label": "vae"}], "properties": {"Node name for S&R": "BizyAir_VAELoader"}, "widgets_values": ["flux/ae.sft"], "shape": 1}, {"id": 66, "type": "BizyAir_VAEEncode", "pos": {"0": -1362, "1": 1190}, "size": {"0": 401.972900390625, "1": 306.923095703125}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 81, "label": "pixels"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 82, "label": "vae"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [83], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "BizyAir_VAEEncode"}, "widgets_values": [], "shape": 1}, {"id": 59, "type": "BizyAir_RandomNoise", "pos": {"0": -514.9483642578125, "1": 500.2052307128906}, "size": {"0": 836.0343627929688, "1": 214.7171630859375}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [74], "slot_index": 0, "shape": 3, "label": "NOISE"}], "properties": {"Node name for S&R": "BizyAir_RandomNoise"}, "widgets_values": [438153234912084, "fixed"], "shape": 1}], "links": [[56, 36, 0, 37, 0, "BIZYAIR_CLIP"], [59, 48, 0, 47, 0, "BIZYAIR_MODEL"], [60, 37, 0, 47, 1, "BIZYAIR_CONDITIONING"], [61, 47, 0, 50, 1, "GUIDER"], [66, 50, 0, 54, 0, "LATENT"], [67, 55, 0, 54, 1, "BIZYAIR_VAE"], [68, 54, 0, 56, 0, "IMAGE"], [72, 48, 0, 58, 0, "BIZYAIR_MODEL"], [73, 58, 0, 50, 3, "SIGMAS"], [74, 59, 0, 50, 0, "NOISE"], [75, 60, 0, 50, 2, "SAMPLER"], [81, 65, 0, 66, 0, "IMAGE"], [82, 55, 0, 66, 1, "BIZYAIR_VAE"], [83, 66, 0, 50, 4, "LATENT"]], "groups": [{"title": "预览图", "bounding": [1234, 747, 478, 772], "color": "#444", "font_size": 24, "flags": {}}, {"title": "参考图", "bounding": [-1729, 745, 347, 773], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "提示词", "bounding": [-467, 746, 489, 772], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"title": "采样器", "bounding": [26, 747, 891, 770], "color": "#8A8", "font_size": 24, "flags": {}}, {"title": "模型", "bounding": [-941, 745, 468, 773], "color": "#A88", "font_size": 24, "flags": {}}, {"title": "VAE编码器", "bounding": [916, 747, 313, 766], "color": "#b06634", "font_size": 24, "flags": {}}, {"title": "随机噪声", "bounding": [-541, 414, 887, 330], "color": "#a1309b", "font_size": 24, "flags": {}}, {"title": "VAE解码器", "bounding": [-1382, 745, 443, 771], "color": "#b06634", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5131581182307071, "offset": [1493.2039081203986, -103.65468260129991]}}, "version": 0.4}