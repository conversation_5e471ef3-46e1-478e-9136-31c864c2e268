## ComfyUI-Manager: installing dependencies done.
[2025-07-23 18:33:20.675] ** ComfyUI startup time: 2025-07-23 18:33:20.675
[2025-07-23 18:33:20.680] ** Platform: Windows
[2025-07-23 18:33:20.680] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-23 18:33:20.680] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-23 18:33:20.682] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-23 18:33:20.682] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-23 18:33:20.686] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-23 18:33:20.688] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-23 18:33:20.688] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-23 18:33:24.332]    8.9 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-23 18:33:24.332] 
[2025-07-23 18:33:29.486] Checkpoint files will always be loaded safely.
[2025-07-23 18:33:29.540] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-23 18:33:29.540] pytorch version: 2.4.1+cpu
[2025-07-23 18:33:29.544] Set vram state to: DISABLED
[2025-07-23 18:33:29.544] Device: cpu
[2025-07-23 18:33:33.133] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-23 18:33:33.199] torchaudio missing, ACE model will be broken
[2025-07-23 18:33:33.211] torchaudio missing, ACE model will be broken
[2025-07-23 18:33:39.733] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-23 18:33:39.735] ComfyUI version: 0.3.45
[2025-07-23 18:33:39.801] ComfyUI frontend version: 1.23.4
[2025-07-23 18:33:39.803] [Prompt Server] web root: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-07-23 18:33:41.755] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py", line 4, in <module>
    import torchaudio
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\__init__.py", line 2, in <module>
    from . import _extension  # noqa  # usort: skip
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\__init__.py", line 38, in <module>
    _load_lib("libtorchaudio")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\utils.py", line 60, in _load_lib
    torch.ops.load_library(path)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\_ops.py", line 1295, in load_library
    ctypes.CDLL(path)
  File "ctypes\__init__.py", line 379, in __init__
FileNotFoundError: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.

[2025-07-23 18:33:41.755] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py module for custom nodes: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
[2025-07-23 18:33:43.066] ----------Jake Upgrade Nodes Loaded----------
[2025-07-23 18:33:43.123] ### Loading: ComfyUI-Manager (V3.34.1)
[2025-07-23 18:33:43.123] [ComfyUI-Manager] network_mode: public
[2025-07-23 18:33:43.380] ### ComfyUI Revision: 150 [9a470e07] *DETACHED | Released on '2025-07-21'
[2025-07-23 18:33:43.435] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-23 18:33:43.437] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-23 18:33:43.439] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-23 18:33:43.571] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-23 18:33:43.580] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-23 18:33:43.740] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-23 18:33:44.017] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-23 18:33:44.278] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-23 18:33:45.238] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-07-23 18:33:49.029] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-23 18:33:49.029] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-23 18:33:49.323] FETCH ComfyRegistry Data: 5/92
[2025-07-23 18:33:50.860] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-23 18:33:50.860] 
	[3m[93m"The best way to predict the future is to create it."[0m[3m - Peter Drucker[0m
[2025-07-23 18:33:50.860] 
[2025-07-23 18:33:50.893] 
Import times for custom nodes:
[2025-07-23 18:33:50.893]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-23 18:33:50.893]    0.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-23 18:33:50.893]    0.2 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade
[2025-07-23 18:33:50.893]    0.3 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-23 18:33:50.893]    1.9 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-23 18:33:50.893]    5.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns
[2025-07-23 18:33:50.893] 
[2025-07-23 18:33:50.893] WARNING: some comfy_extras/ nodes did not import correctly. This may be because they are missing some dependencies.

[2025-07-23 18:33:50.893] IMPORT FAILED: nodes_audio.py
[2025-07-23 18:33:50.893] 
This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
[2025-07-23 18:33:50.893] Please run the update script: update/update_comfyui.bat
[2025-07-23 18:33:50.893] 
[2025-07-23 18:33:52.155] Context impl SQLiteImpl.
[2025-07-23 18:33:52.155] Will assume non-transactional DDL.
[2025-07-23 18:33:52.155] No target revision found.
[2025-07-23 18:33:52.212] Starting server

[2025-07-23 18:33:52.212] To see the GUI go to: http://127.0.0.1:8189
[2025-07-23 18:33:54.243] FETCH ComfyRegistry Data: 10/92
[2025-07-23 18:34:00.096] FETCH ComfyRegistry Data: 15/92
[2025-07-23 18:34:05.273] FETCH ComfyRegistry Data: 20/92
[2025-07-23 18:34:10.122] FETCH ComfyRegistry Data: 25/92
[2025-07-23 18:34:15.013] FETCH ComfyRegistry Data: 30/92
[2025-07-23 18:34:19.980] FETCH ComfyRegistry Data: 35/92
[2025-07-23 18:34:26.538] FETCH ComfyRegistry Data: 40/92
[2025-07-23 18:34:31.985] FETCH ComfyRegistry Data: 45/92
[2025-07-23 18:34:37.038] FETCH ComfyRegistry Data: 50/92
[2025-07-23 18:34:42.125] FETCH ComfyRegistry Data: 55/92
[2025-07-23 18:34:48.360] FETCH ComfyRegistry Data: 60/92
[2025-07-23 18:34:53.627] FETCH ComfyRegistry Data: 65/92
[2025-07-23 18:34:59.588] FETCH ComfyRegistry Data: 70/92
[2025-07-23 18:35:08.193] FETCH ComfyRegistry Data: 75/92
[2025-07-23 18:35:14.875] FETCH ComfyRegistry Data: 80/92
[2025-07-23 18:35:20.681] FETCH ComfyRegistry Data: 85/92
[2025-07-23 18:35:26.721] FETCH ComfyRegistry Data: 90/92
[2025-07-23 18:35:29.531] FETCH ComfyRegistry Data [DONE]
[2025-07-23 18:35:29.961] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-23 18:35:30.024] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-23 18:35:32.466] [ComfyUI-Manager] All startup tasks have been completed.
[2025-07-23 18:41:30.030] got prompt
[2025-07-23 18:41:30.032] Failed to validate prompt for output 9:
[2025-07-23 18:41:30.034] * CheckpointLoaderSimple 4:
[2025-07-23 18:41:30.034]   - Value not in list: ckpt_name: 'dreamshaper_8.safetensors' not in ['DreamShaper_8_pruned.safetensors', 'sd_xl_base_1.0.safetensors', 'v1-5-pruned-emaonly-fp16.safetensors', 'v1-5-pruned-emaonly.safetensors']
[2025-07-23 18:41:30.036] Output will be ignored
[2025-07-23 18:41:30.036] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-23 18:58:27.324] got prompt
[2025-07-23 18:58:27.324] invalid prompt: {'type': 'invalid_prompt', 'message': 'Cannot execute because a node is missing the class_type property.', 'details': "Node ID '#6'", 'extra_info': {}}
[2025-07-23 19:05:03.040] Downloading https://storage.googleapis.com/comfy-registry/yolain/comfyui-easy-use/1.3.1/node.zip to C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\CNR_temp_a054034e-54aa-486a-b582-feab1b9a2850.zip
[2025-07-23 19:05:03.743] 
100%|█████████████████| 1830867/1830867 [00:00<00:00, 5049270.14it/s]
[2025-07-23 19:05:04.334] Extracted zip file to C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-23 19:05:04.618] Install: pip packages
[2025-07-23 19:05:09.251] 
[ComfyUI-Manager] Queued works are completed.
{'install': 1}
[2025-07-23 19:05:09.251] 
After restarting ComfyUI, please refresh the browser.
