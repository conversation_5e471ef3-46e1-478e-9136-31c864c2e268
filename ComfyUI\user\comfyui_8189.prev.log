## ComfyUI-Manager: installing dependencies done.
[2025-07-24 15:24:43.934] ** ComfyUI startup time: 2025-07-24 15:24:43.934
[2025-07-24 15:24:43.935] ** Platform: Windows
[2025-07-24 15:24:43.935] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 15:24:43.935] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-24 15:24:43.936] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 15:24:43.936] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 15:24:43.937] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-24 15:24:43.938] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-24 15:24:43.938] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-24 15:24:46.030]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 15:24:46.031]    1.8 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 15:24:46.031]    4.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 15:24:46.031] 
[2025-07-24 15:24:48.501] Checkpoint files will always be loaded safely.
[2025-07-24 15:24:48.528] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-24 15:24:48.528] pytorch version: 2.4.1+cpu
[2025-07-24 15:24:48.530] Set vram state to: DISABLED
[2025-07-24 15:24:48.530] Device: cpu
[2025-07-24 15:24:50.422] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-24 15:24:50.462] torchaudio missing, ACE model will be broken
[2025-07-24 15:24:50.470] torchaudio missing, ACE model will be broken
[2025-07-24 15:24:53.368] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 15:24:53.368] ComfyUI version: 0.3.45
[2025-07-24 15:24:53.398] ComfyUI frontend version: 1.23.4
[2025-07-24 15:24:53.402] [Prompt Server] web root: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-07-24 15:24:54.689] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py", line 4, in <module>
    import torchaudio
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\__init__.py", line 2, in <module>
    from . import _extension  # noqa  # usort: skip
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\__init__.py", line 38, in <module>
    _load_lib("libtorchaudio")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\utils.py", line 60, in _load_lib
    torch.ops.load_library(path)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\_ops.py", line 1295, in load_library
    ctypes.CDLL(path)
  File "ctypes\__init__.py", line 379, in __init__
FileNotFoundError: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.

[2025-07-24 15:24:54.689] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py module for custom nodes: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
[2025-07-24 15:24:55.252] [92m[BizyAir][0m BizyAir ComfyUI Plugin: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 15:24:55.330] Display name '☁️BizyAir CogView4_6B_Pipe' might differ from the native display name.
[2025-07-24 15:24:55.338] Display name '☁️BizyAir SetUnionControlNetType' might differ from the native display name.
[2025-07-24 15:24:55.338] Display name '☁️BizyAir ControlNetInpaintingAliMamaApply' might differ from the native display name.
[2025-07-24 15:24:55.340] Display name '☁️BizyAir DisableNoise' might differ from the native display name.
[2025-07-24 15:24:55.340] Display name '☁️BizyAir VPScheduler' might differ from the native display name.
[2025-07-24 15:24:55.340] Display name '☁️BizyAir SplitSigmas' might differ from the native display name.
[2025-07-24 15:24:55.341] Display name '☁️BizyAir SplitSigmasDenoise' might differ from the native display name.
[2025-07-24 15:24:55.341] Display name '☁️BizyAir FlipSigmas' might differ from the native display name.
[2025-07-24 15:24:55.341] Display name '☁️BizyAir CFGGuider' might differ from the native display name.
[2025-07-24 15:24:55.343] Display name '☁️BizyAir TrainDatasetAdd' might differ from the native display name.
[2025-07-24 15:24:55.346] Display name '☁️BizyAir CLIPTextEncodeFlux' might differ from the native display name.
[2025-07-24 15:24:55.346] Display name '☁️BizyAir ReferenceLatent' might differ from the native display name.
[2025-07-24 15:24:55.346] Display name '☁️BizyAir FluxGuidance' might differ from the native display name.
[2025-07-24 15:24:55.349] Display name '☁️BizyAir Hy3D_2_1SimpleMeshGen' might differ from the native display name.
[2025-07-24 15:24:55.349] Display name '☁️BizyAir Hy3DExportMesh' might differ from the native display name.
[2025-07-24 15:24:55.356] Display name '☁️BizyAir InstructPixToPixConditioning' might differ from the native display name.
[2025-07-24 15:24:55.360] Display name '☁️BizyAir IPAdapterModelLoader' might differ from the native display name.
[2025-07-24 15:24:55.361] Display name '☁️BizyAir IPAdapterAdvanced' might differ from the native display name.
[2025-07-24 15:24:55.361] Display name '☁️BizyAir IPAdapterStyleComposition' might differ from the native display name.
[2025-07-24 15:24:55.363] Display name '☁️BizyAir JanusModelLoader' might differ from the native display name.
[2025-07-24 15:24:55.364] Display name '☁️BizyAir JanusImageUnderstanding' might differ from the native display name.
[2025-07-24 15:24:55.364] Display name '☁️BizyAir JanusImageGeneration' might differ from the native display name.
[2025-07-24 15:24:55.371] Display name '☁️BizyAir ModelSamplingSD3' might differ from the native display name.
[2025-07-24 15:24:55.371] Display name '☁️BizyAir ModelSamplingFlux' might differ from the native display name.
[2025-07-24 15:24:55.374] Display name '☁️BizyAir NunchakuPulidApply' might differ from the native display name.
[2025-07-24 15:24:55.374] Display name '☁️BizyAir NunchakuPulidLoader' might differ from the native display name.
[2025-07-24 15:24:55.382] Display name '☁️BizyAir TripleCLIPLoader' might differ from the native display name.
[2025-07-24 15:24:55.392] Display name '☁️BizyAir UltimateSDUpscale' might differ from the native display name.
[2025-07-24 15:24:55.396] Display name '☁️BizyAir Wan_LoraLoader' might differ from the native display name.
[2025-07-24 15:24:55.396] Display name '☁️BizyAir Wan_ImageToVideoPipeline' might differ from the native display name.
[2025-07-24 15:24:55.399] Display name '☁️BizyAir Wan_Model_Loader' might differ from the native display name.
[2025-07-24 15:24:55.399] Display name '☁️BizyAir Wan_T2V_Pipeline' might differ from the native display name.
[2025-07-24 15:24:55.412] Display name '☁️BizyAir LoraLoader_Legacy' might differ from the native display name.
[2025-07-24 15:24:55.412] Display name '☁️BizyAir ControlNetLoader_Legacy' might differ from the native display name.
[2025-07-24 15:24:55.413] Display name '☁️BizyAir SamplerCustomAdvanced' might differ from the native display name.
[2025-07-24 15:24:55.414] Display name '☁️BizyAir BasicGuider' might differ from the native display name.
[2025-07-24 15:24:55.414] Display name '☁️BizyAir BasicScheduler' might differ from the native display name.
[2025-07-24 15:24:55.414] Display name '☁️BizyAir DualCLIPLoader' might differ from the native display name.
[2025-07-24 15:24:55.415] Display name '☁️BizyAir KSamplerSelect' might differ from the native display name.
[2025-07-24 15:24:55.415] Display name '☁️BizyAir RandomNoise' might differ from the native display name.
[2025-07-24 15:24:55.415] Display name '☁️BizyAir InpaintModelConditioning' might differ from the native display name.
[2025-07-24 15:24:55.416] Display name '☁️BizyAir InpaintModelConditioning_v2' might differ from the native display name.
[2025-07-24 15:24:55.417] Display name '☁️BizyAir ConditioningZeroOut' might differ from the native display name.
[2025-07-24 15:24:55.417] Display name '☁️BizyAir ConditioningSetTimestepRange' might differ from the native display name.
[2025-07-24 15:24:55.419] Display name '☁️BizyAir PassParameter' might differ from the native display name.
[2025-07-24 15:24:56.354] 

[92m[BizyAir][0m Model hosting service initialized.

[2025-07-24 15:24:56.354] 
[2025-07-24 15:24:59.984] [34m[ComfyUI-Easy-Use] server: [0mv1.3.1 [92mLoaded[0m
[2025-07-24 15:24:59.984] [34m[ComfyUI-Easy-Use] web root: [0mC:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-07-24 15:25:00.040] ----------Jake Upgrade Nodes Loaded----------
[2025-07-24 15:25:00.061] ### Loading: ComfyUI-Manager (V3.34.1)
[2025-07-24 15:25:00.064] [ComfyUI-Manager] network_mode: public
[2025-07-24 15:25:00.198] ### ComfyUI Revision: 150 [9a470e07] *DETACHED | Released on '2025-07-21'
[2025-07-24 15:25:00.229] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-24 15:25:00.230] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-24 15:25:00.231] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-24 15:25:00.289] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-24 15:25:00.346] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-24 15:25:00.386] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-24 15:25:00.505] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-24 15:25:00.604] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-24 15:25:01.161] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-07-24 15:25:01.221] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\__init__.py", line 1, in <module>
    from .faceanalysis import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\faceanalysis.py", line 16, in <module>
    raise Exception("Please install either dlib or insightface to use this node.")
Exception: Please install either dlib or insightface to use this node.

[2025-07-24 15:25:01.221] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis module for custom nodes: Please install either dlib or insightface to use this node.
[2025-07-24 15:25:02.417] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-24 15:25:02.417] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-24 15:25:03.260] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-24 15:25:03.260] 
	[3m[93m"The artist's world is limitless. It can be found anywhere, far from where he lives or a few feet away. It is always on his doorstep."[0m[3m - Paul Strand[0m
[2025-07-24 15:25:03.260] 
[2025-07-24 15:25:03.270] 
Import times for custom nodes:
[2025-07-24 15:25:03.270]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-24 15:25:03.270]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis
[2025-07-24 15:25:03.270]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-24 15:25:03.270]    0.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade
[2025-07-24 15:25:03.272]    0.2 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 15:25:03.272]    1.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-24 15:25:03.272]    1.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 15:25:03.272]    2.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns
[2025-07-24 15:25:03.272]    3.6 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 15:25:03.273] 
[2025-07-24 15:25:03.273] WARNING: some comfy_extras/ nodes did not import correctly. This may be because they are missing some dependencies.

[2025-07-24 15:25:03.273] IMPORT FAILED: nodes_audio.py
[2025-07-24 15:25:03.273] 
This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
[2025-07-24 15:25:03.273] Please run the update script: update/update_comfyui.bat
[2025-07-24 15:25:03.273] 
[2025-07-24 15:25:03.765] Context impl SQLiteImpl.
[2025-07-24 15:25:03.765] Will assume non-transactional DDL.
[2025-07-24 15:25:03.766] No target revision found.
[2025-07-24 15:25:03.793] Starting server

[2025-07-24 15:25:03.794] To see the GUI go to: http://127.0.0.1:8189
[2025-07-24 15:25:05.796] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\bizyengine\core\common\client.py:69: UserWarning: invalid api_key
  warnings.warn("invalid api_key")
[2025-07-24 15:25:05.922] FETCH ComfyRegistry Data: 5/92
[2025-07-24 15:25:11.030] FETCH ComfyRegistry Data: 10/92
[2025-07-24 15:25:16.578] FETCH ComfyRegistry Data: 15/92
[2025-07-24 15:25:21.799] FETCH ComfyRegistry Data: 20/92
[2025-07-24 15:25:26.947] FETCH ComfyRegistry Data: 25/92
[2025-07-24 15:25:31.758] FETCH ComfyRegistry Data: 30/92
[2025-07-24 15:25:36.840] FETCH ComfyRegistry Data: 35/92
[2025-07-24 15:25:41.726] FETCH ComfyRegistry Data: 40/92
[2025-07-24 15:25:46.700] FETCH ComfyRegistry Data: 45/92
[2025-07-24 15:25:51.996] FETCH ComfyRegistry Data: 50/92
[2025-07-24 15:25:56.787] FETCH ComfyRegistry Data: 55/92
[2025-07-24 15:26:02.250] FETCH ComfyRegistry Data: 60/92
[2025-07-24 15:26:03.410] [31m[聊天请求-req-1668409538176][0m 处理请求时发生错误: 'tuple' object does not support the context manager protocol
[2025-07-24 15:26:07.526] FETCH ComfyRegistry Data: 65/92
[2025-07-24 15:26:12.288] FETCH ComfyRegistry Data: 70/92
[2025-07-24 15:26:17.505] FETCH ComfyRegistry Data: 75/92
[2025-07-24 15:26:24.780] FETCH ComfyRegistry Data: 80/92
[2025-07-24 15:26:30.973] FETCH ComfyRegistry Data: 85/92
[2025-07-24 15:26:34.477] got prompt
[2025-07-24 15:26:42.840] model weight dtype torch.float32, manual cast: None
[2025-07-24 15:26:42.965] model_type EPS
[2025-07-24 15:26:43.726] FETCH ComfyRegistry Data: 90/92
[2025-07-24 15:26:46.403] FETCH ComfyRegistry Data [DONE]
[2025-07-24 15:26:51.764] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-24 15:26:52.099] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-24 15:26:53.501] [ComfyUI-Manager] All startup tasks have been completed.
[2025-07-24 15:26:59.208] Using split attention in VAE
[2025-07-24 15:26:59.235] Using split attention in VAE
[2025-07-24 15:27:01.239] VAE load device: cpu, offload device: cpu, dtype: torch.float32
[2025-07-24 15:27:03.078] Requested to load SD1ClipModel
[2025-07-24 15:27:03.101] loaded completely 9.5367431640625e+25 235.84423828125 True
[2025-07-24 15:27:03.105] CLIP/text encoder model load device: cpu, offload device: cpu, current: cpu, dtype: torch.float16
[2025-07-24 15:27:05.108] Requested to load AutoencoderKL
[2025-07-24 15:27:05.153] loaded completely 9.5367431640625e+25 319.11416244506836 True
[2025-07-24 15:27:13.417] Requested to load BaseModel
[2025-07-24 15:27:13.457] loaded completely 9.5367431640625e+25 3278.812271118164 True
[2025-07-24 15:30:52.906] 
100%|█████████████████████████████████████| 20/20 [03:39<00:00, 10.89s/it]
100%|█████████████████████████████████████| 20/20 [03:39<00:00, 10.97s/it]
[2025-07-24 15:31:03.393] Prompt executed in 268.78 seconds
[2025-07-24 15:34:01.384] got prompt
[2025-07-24 15:34:01.539] !!! Exception during processing !!! Error while deserializing header: MetadataIncompleteBuffer

File path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\models\checkpoints\sd_xl_base_1.0.safetensors

The safetensors file is corrupt/incomplete. Check the file size and make sure you have copied/downloaded it correctly.
[2025-07-24 15:34:01.546] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\utils.py", line 59, in load_torch_file
    with safetensors.safe_open(ckpt, framework="pt", device=device.type) as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
safetensors_rust.SafetensorError: Error while deserializing header: MetadataIncompleteBuffer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 573, in load_checkpoint
    out = comfy.sd.load_checkpoint_guess_config(ckpt_path, output_vae=True, output_clip=True, embedding_directory=folder_paths.get_folder_paths("embeddings"))
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\sd.py", line 1012, in load_checkpoint_guess_config
    sd, metadata = comfy.utils.load_torch_file(ckpt_path, return_metadata=True)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\utils.py", line 74, in load_torch_file
    raise ValueError("{}\n\nFile path: {}\n\nThe safetensors file is corrupt/incomplete. Check the file size and make sure you have copied/downloaded it correctly.".format(message, ckpt))
ValueError: Error while deserializing header: MetadataIncompleteBuffer

File path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\models\checkpoints\sd_xl_base_1.0.safetensors

The safetensors file is corrupt/incomplete. Check the file size and make sure you have copied/downloaded it correctly.

[2025-07-24 15:34:01.546] Prompt executed in 0.16 seconds
[2025-07-24 15:37:07.877] got prompt
[2025-07-24 15:37:07.879] Failed to validate prompt for output 8:
[2025-07-24 15:37:07.880] * KSampler 6:
[2025-07-24 15:37:07.880]   - Value not in list: sampler_name: 'euler_a' not in (list of length 40)
[2025-07-24 15:37:07.880] Output will be ignored
[2025-07-24 15:37:07.880] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 15:37:21.318] got prompt
[2025-07-24 15:37:21.319] Failed to validate prompt for output 8:
[2025-07-24 15:37:21.319] * KSampler 6:
[2025-07-24 15:37:21.319]   - Value not in list: sampler_name: 'euler_a' not in (list of length 40)
[2025-07-24 15:37:21.319] Output will be ignored
[2025-07-24 15:37:21.319] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 15:37:29.400] got prompt
[2025-07-24 15:37:30.114] model weight dtype torch.float32, manual cast: None
[2025-07-24 15:37:30.117] model_type EPS
[2025-07-24 15:37:32.065] Using split attention in VAE
[2025-07-24 15:37:32.072] Using split attention in VAE
[2025-07-24 15:37:32.597] VAE load device: cpu, offload device: cpu, dtype: torch.float32
[2025-07-24 15:37:32.852] Requested to load SD1ClipModel
[2025-07-24 15:37:32.863] loaded completely 9.5367431640625e+25 235.84423828125 True
[2025-07-24 15:37:32.868] CLIP/text encoder model load device: cpu, offload device: cpu, current: cpu, dtype: torch.float16
[2025-07-24 15:37:34.221] Requested to load AutoencoderKL
[2025-07-24 15:37:34.236] loaded completely 9.5367431640625e+25 319.11416244506836 True
[2025-07-24 15:37:40.613] Requested to load BaseModel
[2025-07-24 15:37:40.649] loaded completely 9.5367431640625e+25 3278.812271118164 True
[2025-07-24 15:40:25.620] 
100%|█████████████████████████████████████| 15/15 [02:44<00:00, 10.77s/it]
100%|█████████████████████████████████████| 15/15 [02:44<00:00, 11.00s/it]
[2025-07-24 15:40:36.111] Prompt executed in 186.70 seconds
[2025-07-24 16:07:31.334] got prompt
[2025-07-24 16:07:31.346] Failed to validate prompt for output 10:
[2025-07-24 16:07:31.346] * CheckpointLoaderSimple 1:
[2025-07-24 16:07:31.346]   - Value not in list: ckpt_name: 'dreamshaper_8.safetensors' not in ['DreamShaper_8_pruned.safetensors', 'sd_xl_base_1.0.safetensors', 'v1-5-pruned-emaonly-fp16.safetensors', 'v1-5-pruned-emaonly.safetensors']
[2025-07-24 16:07:31.347] * IPAdapterAdvanced 4:
[2025-07-24 16:07:31.347]   - Failed to convert an input value to a FLOAT value: start_at, linear, could not convert string to float: 'linear'
[2025-07-24 16:07:31.347]   - Value not in list: weight_type: '0' not in ['linear', 'ease in', 'ease out', 'ease in-out', 'reverse in-out', 'weak input', 'weak output', 'weak middle', 'strong middle', 'style transfer', 'composition', 'strong style transfer']
[2025-07-24 16:07:31.348]   - Value not in list: combine_embeds: '1' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-24 16:07:31.348]   - Value not in list: embeds_scaling: '1' not in ['V only', 'K+V', 'K+V w/ C penalty', 'K+mean(V) w/ C penalty']
[2025-07-24 16:07:31.348] * KSampler 8:
[2025-07-24 16:07:31.348]   - Value not in list: sampler_name: 'euler_a' not in (list of length 40)
[2025-07-24 16:07:31.349] * VAELoader 11:
[2025-07-24 16:07:31.349]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 16:07:31.349] Output will be ignored
[2025-07-24 16:07:31.349] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 16:07:46.243] got prompt
[2025-07-24 16:07:46.244] Failed to validate prompt for output 10:
[2025-07-24 16:07:46.247] * IPAdapterAdvanced 4:
[2025-07-24 16:07:46.247]   - Failed to convert an input value to a FLOAT value: start_at, linear, could not convert string to float: 'linear'
[2025-07-24 16:07:46.248]   - Value not in list: weight_type: '0' not in ['linear', 'ease in', 'ease out', 'ease in-out', 'reverse in-out', 'weak input', 'weak output', 'weak middle', 'strong middle', 'style transfer', 'composition', 'strong style transfer']
[2025-07-24 16:07:46.248]   - Value not in list: combine_embeds: '1' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-24 16:07:46.248]   - Value not in list: embeds_scaling: '1' not in ['V only', 'K+V', 'K+V w/ C penalty', 'K+mean(V) w/ C penalty']
[2025-07-24 16:07:46.249] * KSampler 8:
[2025-07-24 16:07:46.249]   - Value not in list: sampler_name: 'euler_a' not in (list of length 40)
[2025-07-24 16:07:46.249] * VAELoader 11:
[2025-07-24 16:07:46.249]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 16:07:46.249] Output will be ignored
[2025-07-24 16:07:46.249] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 16:13:55.452] got prompt
[2025-07-24 16:16:34.993] 
100%|█████████████████████████████████████| 15/15 [02:38<00:00, 10.50s/it]
100%|█████████████████████████████████████| 15/15 [02:38<00:00, 10.59s/it]
[2025-07-24 16:16:43.484] Prompt executed in 168.03 seconds
[2025-07-24 16:18:26.247] got prompt
[2025-07-24 16:54:40.972] got prompt
[2025-07-24 16:54:41.002] Failed to validate prompt for output 10:
[2025-07-24 16:54:41.002] * CheckpointLoaderSimple 1:
[2025-07-24 16:54:41.005]   - Value not in list: ckpt_name: 'dreamshaper_8.safetensors' not in ['DreamShaper_8_pruned.safetensors', 'sd_xl_base_1.0.safetensors', 'v1-5-pruned-emaonly-fp16.safetensors', 'v1-5-pruned-emaonly.safetensors']
[2025-07-24 16:54:41.006] * IPAdapterFaceID 4:
[2025-07-24 16:54:41.006]   - Value not in list: combine_embeds: '0' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-24 16:54:41.006]   - Value not in list: embeds_scaling: 'False' not in ['V only', 'K+V', 'K+V w/ C penalty', 'K+mean(V) w/ C penalty']
[2025-07-24 16:54:41.007]   - Failed to convert an input value to a FLOAT value: end_at, concat, could not convert string to float: 'concat'
[2025-07-24 16:54:41.008] * KSampler 8:
[2025-07-24 16:54:41.008]   - Value not in list: sampler_name: 'euler_a' not in (list of length 40)
[2025-07-24 16:54:41.009] * VAELoader 11:
[2025-07-24 16:54:41.009]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 16:54:41.009] Output will be ignored
[2025-07-24 16:54:41.010] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 16:55:07.755] got prompt
[2025-07-24 16:55:07.758] Failed to validate prompt for output 10:
[2025-07-24 16:55:07.759] * IPAdapterFaceID 4:
[2025-07-24 16:55:07.759]   - Value not in list: combine_embeds: '0' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-24 16:55:07.759]   - Value not in list: embeds_scaling: 'False' not in ['V only', 'K+V', 'K+V w/ C penalty', 'K+mean(V) w/ C penalty']
[2025-07-24 16:55:07.760]   - Failed to convert an input value to a FLOAT value: end_at, concat, could not convert string to float: 'concat'
[2025-07-24 16:55:07.760] * KSampler 8:
[2025-07-24 16:55:07.760]   - Value not in list: sampler_name: 'euler_a' not in (list of length 40)
[2025-07-24 16:55:07.760] * VAELoader 11:
[2025-07-24 16:55:07.761]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 16:55:07.761] Output will be ignored
[2025-07-24 16:55:07.762] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 16:55:42.913] got prompt
[2025-07-24 16:55:42.919] Failed to validate prompt for output 10:
[2025-07-24 16:55:42.920] * IPAdapterFaceID 4:
[2025-07-24 16:55:42.920]   - Value not in list: combine_embeds: '0' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-24 16:55:42.920]   - Value not in list: embeds_scaling: 'False' not in ['V only', 'K+V', 'K+V w/ C penalty', 'K+mean(V) w/ C penalty']
[2025-07-24 16:55:42.921] * KSampler 8:
[2025-07-24 16:55:42.921]   - Value not in list: sampler_name: 'euler_a' not in (list of length 40)
[2025-07-24 16:55:42.921] * VAELoader 11:
[2025-07-24 16:55:42.922]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 16:55:42.922] Output will be ignored
[2025-07-24 16:55:42.923] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 17:00:41.271] got prompt
[2025-07-24 17:00:41.278] Failed to validate prompt for output 10:
[2025-07-24 17:00:41.278] * IPAdapterFaceID 4:
[2025-07-24 17:00:41.278]   - Value not in list: combine_embeds: '0' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-24 17:00:41.278]   - Value not in list: embeds_scaling: 'False' not in ['V only', 'K+V', 'K+V w/ C penalty', 'K+mean(V) w/ C penalty']
[2025-07-24 17:00:41.278] * KSampler 8:
[2025-07-24 17:00:41.279]   - Value not in list: sampler_name: 'euler_a' not in (list of length 40)
[2025-07-24 17:00:41.279] * VAELoader 11:
[2025-07-24 17:00:41.279]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 17:00:41.279] Output will be ignored
[2025-07-24 17:00:41.280] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 17:01:09.738] got prompt
[2025-07-24 17:01:09.743] Failed to validate prompt for output 10:
[2025-07-24 17:01:09.744] * IPAdapterFaceID 4:
[2025-07-24 17:01:09.745]   - Value not in list: combine_embeds: '0' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-24 17:01:09.745]   - Value not in list: embeds_scaling: 'False' not in ['V only', 'K+V', 'K+V w/ C penalty', 'K+mean(V) w/ C penalty']
[2025-07-24 17:01:09.746] * VAELoader 11:
[2025-07-24 17:01:09.746]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 17:01:09.747] Output will be ignored
[2025-07-24 17:01:09.748] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 17:01:17.574] got prompt
[2025-07-24 17:01:17.580] Failed to validate prompt for output 10:
[2025-07-24 17:01:17.581] * IPAdapterFaceID 4:
[2025-07-24 17:01:17.582]   - Value not in list: combine_embeds: '0' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-24 17:01:17.583] * VAELoader 11:
[2025-07-24 17:01:17.584]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 17:01:17.584] Output will be ignored
[2025-07-24 17:01:17.584] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 17:01:31.147] got prompt
[2025-07-24 17:01:31.156] Failed to validate prompt for output 10:
[2025-07-24 17:01:31.158] * VAELoader 11:
[2025-07-24 17:01:31.159]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 17:01:31.161] Output will be ignored
[2025-07-24 17:01:31.162] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 17:01:53.411] got prompt
[2025-07-24 17:01:53.417] Failed to validate prompt for output 10:
[2025-07-24 17:01:53.418] * VAELoader 11:
[2025-07-24 17:01:53.418]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 17:01:53.419] Output will be ignored
[2025-07-24 17:01:53.419] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 17:02:05.027] got prompt
[2025-07-24 17:02:05.032] Failed to validate prompt for output 10:
[2025-07-24 17:02:05.032] * VAELoader 11:
[2025-07-24 17:02:05.032]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 17:02:05.033] Output will be ignored
[2025-07-24 17:02:05.034] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 17:02:29.143] got prompt
[2025-07-24 17:02:29.147] Failed to validate prompt for output 10:
[2025-07-24 17:02:29.148] * VAELoader 11:
[2025-07-24 17:02:29.148]   - Value not in list: vae_name: 'vae-ft-mse-840000-ema-pruned.safetensors' not in ['taesd', 'taesdxl', 'taesd3', 'taef1']
[2025-07-24 17:02:29.148] Output will be ignored
[2025-07-24 17:02:29.148] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 17:02:47.252] got prompt
[2025-07-24 17:56:31.652] Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
[2025-07-24 21:29:12.815] 
100%|█████████████████████████████████| 15/15 [5:09:22<00:00, 1549.96s/it]
100%|█████████████████████████████████| 15/15 [5:09:22<00:00, 1237.49s/it]
[2025-07-24 22:12:51.076] Prompt executed in 05:54:24
[2025-07-24 22:12:54.219] VAE load device: cpu, offload device: cpu, dtype: torch.float32
[2025-07-24 22:12:54.279] !!! Exception during processing !!! Error while deserializing header: MetadataIncompleteBuffer

File path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\models\checkpoints\sd_xl_base_1.0.safetensors

The safetensors file is corrupt/incomplete. Check the file size and make sure you have copied/downloaded it correctly.
[2025-07-24 22:12:54.287] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\utils.py", line 59, in load_torch_file
    with safetensors.safe_open(ckpt, framework="pt", device=device.type) as f:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
safetensors_rust.SafetensorError: Error while deserializing header: MetadataIncompleteBuffer

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 573, in load_checkpoint
    out = comfy.sd.load_checkpoint_guess_config(ckpt_path, output_vae=True, output_clip=True, embedding_directory=folder_paths.get_folder_paths("embeddings"))
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\sd.py", line 1012, in load_checkpoint_guess_config
    sd, metadata = comfy.utils.load_torch_file(ckpt_path, return_metadata=True)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\utils.py", line 74, in load_torch_file
    raise ValueError("{}\n\nFile path: {}\n\nThe safetensors file is corrupt/incomplete. Check the file size and make sure you have copied/downloaded it correctly.".format(message, ckpt))
ValueError: Error while deserializing header: MetadataIncompleteBuffer

File path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\models\checkpoints\sd_xl_base_1.0.safetensors

The safetensors file is corrupt/incomplete. Check the file size and make sure you have copied/downloaded it correctly.

[2025-07-24 22:12:54.293] Prompt executed in 0.93 seconds
