{"last_node_id": 53, "last_link_id": 80, "nodes": [{"id": 27, "type": "BizyAir_KSampler", "pos": {"0": 1804.511962890625, "1": -735.066650390625}, "size": {"0": 328.00860595703125, "1": 643.0760498046875}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 78, "label": "model"}, {"name": "positive", "type": "BIZYAIR_CONDITIONING", "link": 74, "slot_index": 1, "label": "positive"}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "link": 41, "slot_index": 2, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": 65, "slot_index": 3, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [45], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "BizyAir_KSampler"}, "widgets_values": [1, "fixed", 20, 8, "euler", "normal", 1], "shape": 1}, {"id": 53, "type": "Note", "pos": {"0": 255, "1": -1199}, "size": {"0": 1984.5662841796875, "1": 152.79811096191406}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["ControlNet 工作流示例。\n\nBizyAir Load ControlNet Model 节点，可以用于加载 ControlNet。BizyAir Apply ContolNet 模型，可以将 ControlNet 应用到控制条件上。使得最终生成的图片，与参考图片有一定的一致性。"], "color": "#432", "bgcolor": "#653"}, {"id": 51, "type": "LoadImage", "pos": {"0": 12, "1": -738}, "size": {"0": 296.5235900878906, "1": 648.4070434570312}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [75], "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"], "shape": 1}, {"id": 35, "type": "PreviewImage", "pos": {"0": 2546.86767578125, "1": -740.98876953125}, "size": {"0": 379.95660400390625, "1": 651.4000244140625}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 46, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}, {"id": 28, "type": "BizyAir_CheckpointLoaderSimple", "pos": {"0": 350, "1": -737}, "size": [429.2432274555074, 639.8076921882209], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "BIZYAIR_MODEL", "links": [78], "shape": 3, "label": "model"}, {"name": "clip", "type": "BIZYAIR_CLIP", "links": [79, 80], "slot_index": 1, "shape": 3, "label": "clip"}, {"name": "vae", "type": "BIZYAIR_VAE", "links": [44], "slot_index": 2, "shape": 3, "label": "vae"}], "properties": {"Node name for S&R": "BizyAir_CheckpointLoaderSimple"}, "widgets_values": ["sdxl/Juggernaut-XL_v9_RunDiffusionPhoto_v2.safetensors"], "shape": 1}, {"id": 31, "type": "BizyAir_CLIPTextEncode", "pos": {"0": 831, "1": -736}, "size": {"0": 361.652587890625, "1": 296.0230407714844}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 79, "slot_index": 0, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [73], "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["A futuristic spaceship soaring through a star-filled galaxy with intricate details and advanced technology, reflecting a vibrant blend of sci-fi"], "shape": 1}, {"id": 32, "type": "BizyAir_CLIPTextEncode", "pos": {"0": 839, "1": -388}, "size": {"0": 356.98358154296875, "1": 300.0760192871094}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 80, "slot_index": 0, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [41], "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["blurry, noisy, messy, lowres, jpeg, artifacts, ill, distorted, malformed"], "shape": 1}, {"id": 33, "type": "EmptyLatentImage", "pos": {"0": 1223, "1": -352}, "size": [545.974827455507, 254.8076921882216], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [65], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1], "shape": 1}, {"id": 49, "type": "BizyAir_ControlNetLoader", "pos": {"0": 1229.92822265625, "1": -731}, "size": [260.3999938964844, 279.49549218822176], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "BIZYAIR_CONTROL_NET", "links": [72], "slot_index": 0, "shape": 3, "label": "CONTROL_NET"}], "properties": {"Node name for S&R": "BizyAir_ControlNetLoader"}, "widgets_values": ["sdxl/diffusion_pytorch_model_promax.safetensors"], "shape": 1}, {"id": 50, "type": "BizyAir_ControlNetApply", "pos": {"0": 1504.92822265625, "1": -738}, "size": [269.367127455507, 282.5672921882217], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "conditioning", "type": "BIZYAIR_CONDITIONING", "link": 73, "slot_index": 0, "label": "conditioning"}, {"name": "control_net", "type": "BIZYAIR_CONTROL_NET", "link": 72, "label": "control_net"}, {"name": "image", "type": "IMAGE", "link": 75, "slot_index": 2, "label": "image"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [74], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_ControlNetApply"}, "widgets_values": [1], "shape": 1}, {"id": 34, "type": "BizyAir_VAEDecode", "pos": {"0": 2164, "1": -735}, "size": [338.48892745550665, 640.7358921882219], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 45, "label": "samples"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 44, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [46], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAir_VAEDecode"}, "widgets_values": [], "shape": 1}], "links": [[41, 32, 0, 27, 2, "BIZYAIR_CONDITIONING"], [44, 28, 2, 34, 1, "BIZYAIR_VAE"], [45, 27, 0, 34, 0, "LATENT"], [46, 34, 0, 35, 0, "IMAGE"], [65, 33, 0, 27, 3, "LATENT"], [72, 49, 0, 50, 1, "BIZYAIR_CONTROL_NET"], [73, 31, 0, 50, 0, "BIZYAIR_CONDITIONING"], [74, 50, 0, 27, 1, "BIZYAIR_CONDITIONING"], [75, 51, 0, 50, 2, "IMAGE"], [78, 28, 0, 27, 0, "BIZYAIR_MODEL"], [79, 28, 1, 31, 0, "BIZYAIR_CLIP"], [80, 28, 1, 32, 0, "BIZYAIR_CLIP"]], "groups": [{"title": "预览图", "bounding": [2524, -815, 405, 739], "color": "#444", "font_size": 24, "flags": {}}, {"title": "提示词", "bounding": [811, -816, 398, 741], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"title": "模型", "bounding": [330, -817, 477, 741], "color": "#a1309b", "font_size": 24, "flags": {}}, {"title": "参考图", "bounding": [-4, -817, 331, 742], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "采样器", "bounding": [1788, -815, 361, 742], "color": "#8A8", "font_size": 24, "flags": {}}, {"title": "VAE", "bounding": [2150, -815, 372, 740], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "垫图", "bounding": [1212, -430, 575, 355], "color": "#b06634", "font_size": 24, "flags": {}}, {"title": "ControlNet", "bounding": [1211, -816, 578, 382], "color": "#A88", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.683013455365071, "offset": [-55.9778274555082, 961.011307811778]}}, "version": 0.4}