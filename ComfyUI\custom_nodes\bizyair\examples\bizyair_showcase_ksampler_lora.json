{"last_node_id": 52, "last_link_id": 77, "nodes": [{"id": 32, "type": "BizyAir_CLIPTextEncode", "pos": {"0": 1229.134521484375, "1": 178.42047119140625}, "size": {"0": 368.4900817871094, "1": 249.05618286132812}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 70, "slot_index": 0, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [41], "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["blurry, noisy, messy, lowres, jpeg, artifacts, ill, distorted, malformed"], "shape": 1}, {"id": 31, "type": "BizyAir_CLIPTextEncode", "pos": {"0": 1230.134521484375, "1": -134.5795440673828}, "size": {"0": 360.8901062011719, "1": 268.3561706542969}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 69, "slot_index": 0, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [77], "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["in a peaceful spring morning a dog is sitting in a park on a bench. high quality, detailed, diffuse light"], "shape": 1}, {"id": 27, "type": "BizyAir_KSampler", "pos": {"0": 1640.576904296875, "1": -134.5794677734375}, "size": {"0": 337.8901062011719, "1": 562.0562133789062}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 76, "label": "model"}, {"name": "positive", "type": "BIZYAIR_CONDITIONING", "link": 77, "slot_index": 1, "label": "positive"}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "link": 41, "slot_index": 2, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": 65, "slot_index": 3, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [45], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "BizyAir_KSampler"}, "widgets_values": [2, "fixed", 20, 8, "euler", "normal", 1], "shape": 1}, {"id": 52, "type": "Note", "pos": {"0": 570, "1": -417}, "size": {"0": 1711.39013671875, "1": 80.05618286132812}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["LoRA 工作流示例：\n本工作流因为使用了 BizyAir Load LoRA 节点，加载了一个水彩风格的 LoRA 模型。所以生成的图像也是水彩风格的。\n你可以尝试去掉 BizyAir Load LoRA 节点，查看生成图像的变化。"], "color": "#432", "bgcolor": "#653"}, {"id": 34, "type": "BizyAir_VAEDecode", "pos": {"0": 2017.01123046875, "1": -128.8732452392578}, "size": [370.70366953124994, 551.8872552392578], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 45, "label": "samples"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 44, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [46], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAir_VAEDecode"}, "widgets_values": [], "shape": 1}, {"id": 35, "type": "PreviewImage", "pos": {"0": 2439, "1": -134}, "size": {"0": 354.79010009765625, "1": 548.356201171875}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 46, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}, {"id": 48, "type": "BizyAir_LoraLoader", "pos": {"0": 700.35546875, "1": -130.1377410888672}, "size": [499.0241699218748, 562.4253093896485], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 66, "slot_index": 0, "label": "model"}, {"name": "clip", "type": "BIZYAIR_CLIP", "link": 67, "slot_index": 1, "label": "clip"}], "outputs": [{"name": "MODEL", "type": "BIZYAIR_MODEL", "links": [76], "slot_index": 0, "shape": 3, "label": "MODEL"}, {"name": "CLIP", "type": "BIZYAIR_CLIP", "links": [69, 70], "slot_index": 1, "shape": 3, "label": "CLIP"}], "properties": {"Node name for S&R": "BizyAir_LoraLoader"}, "widgets_values": ["sdxl/watercolor_v1_sdxl_lora.safetensors", 1, 1], "shape": 1}, {"id": 28, "type": "BizyAir_CheckpointLoaderSimple", "pos": {"0": 285, "1": -123}, "size": [377.85868999999957, 547.6245200000001], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "BIZYAIR_MODEL", "links": [66], "shape": 3, "label": "model"}, {"name": "clip", "type": "BIZYAIR_CLIP", "links": [67], "shape": 3, "label": "clip"}, {"name": "vae", "type": "BIZYAIR_VAE", "links": [44], "slot_index": 2, "shape": 3, "label": "vae"}], "properties": {"Node name for S&R": "BizyAir_CheckpointLoaderSimple"}, "widgets_values": ["sdxl/Juggernaut-XL_v9_RunDiffusionPhoto_v2.safetensors"], "shape": 1}, {"id": 33, "type": "EmptyLatentImage", "pos": {"0": 1271, "1": 656}, "size": {"0": 477.9900817871094, "1": 106}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [65], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1], "shape": 1}], "links": [[41, 32, 0, 27, 2, "BIZYAIR_CONDITIONING"], [44, 28, 2, 34, 1, "BIZYAIR_VAE"], [45, 27, 0, 34, 0, "LATENT"], [46, 34, 0, 35, 0, "IMAGE"], [65, 33, 0, 27, 3, "LATENT"], [66, 28, 0, 48, 0, "BIZYAIR_MODEL"], [67, 28, 1, 48, 1, "BIZYAIR_CLIP"], [69, 48, 1, 31, 0, "BIZYAIR_CLIP"], [70, 48, 1, 32, 0, "BIZYAIR_CLIP"], [76, 48, 0, 27, 0, "BIZYAIR_MODEL"], [77, 31, 0, 27, 1, "BIZYAIR_CONDITIONING"]], "groups": [{"title": "垫图", "bounding": [1258, 544, 500, 262], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "VAE", "bounding": [1996, -210, 413, 656], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "预览图", "bounding": [2410, -210, 393, 655], "color": "#444", "font_size": 24, "flags": {}}, {"title": "LoRA", "bounding": [679, -208, 533, 655], "color": "#a1309b", "font_size": 24, "flags": {}}, {"title": "提示词", "bounding": [1211, -211, 407, 656], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"title": "采样器", "bounding": [1622, -210, 372, 655], "color": "#8A8", "font_size": 24, "flags": {}}, {"title": "模型", "bounding": [278, -208, 399, 654], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.620921323059155, "offset": [-807.8045899999994, 478.87159000000014]}}, "version": 0.4}