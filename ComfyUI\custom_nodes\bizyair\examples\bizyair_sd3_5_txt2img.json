{"last_node_id": 318, "last_link_id": 627, "nodes": [{"id": 105, "type": "Note", "pos": {"0": -528, "1": -261}, "size": {"0": 210, "1": 110.18948364257812}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["Make sure the resolution is multiple of 64 pixels and adds up to around 1 megapixel. "], "color": "#432", "bgcolor": "#653"}, {"id": 303, "type": "BizyAir_TripleCLIPLoader", "pos": {"0": -1142, "1": 449}, "size": [349.3921858350384, 202.23654626210953], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_CLIP", "type": "BIZYAIR_CLIP", "links": [627], "slot_index": 0, "shape": 3, "label": "BIZYAIR_CLIP"}], "properties": {"Node name for S&R": "BizyAir_TripleCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "clip_g.safetensors", "t5xxl_fp16.safetensors"]}, {"id": 302, "type": "BizyAir_CheckpointLoaderSimple", "pos": {"0": -1143, "1": 214}, "size": [350.8562858350384, 187.94204626210978], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "BIZYAIR_MODEL", "links": [617], "slot_index": 0, "shape": 3, "label": "model"}, {"name": "clip", "type": "BIZYAIR_CLIP", "links": null, "shape": 3, "label": "clip"}, {"name": "vae", "type": "BIZYAIR_VAE", "links": [619], "slot_index": 2, "shape": 3, "label": "vae"}], "properties": {"Node name for S&R": "BizyAir_CheckpointLoaderSimple"}, "widgets_values": ["sd3.5_large.safetensors"]}, {"id": 316, "type": "Reroute", "pos": {"0": -772, "1": 741}, "size": [75, 26], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 627, "label": ""}], "outputs": [{"name": "", "type": "BIZYAIR_CLIP", "links": [624, 625], "slot_index": 0, "label": ""}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 304, "type": "BizyAir_CLIPTextEncode", "pos": {"0": -752.07177734375, "1": 213}, "size": [371.80428583503794, 169.6933462621098], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 625, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [615], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["insulated cup nestled within, displaying the text 'bizyair' in bold letters. The surrounding area is a simple, pure canvas with no additional distractions, highlighting the central theme of the product. Ensure the overall composition is both elegant and serene, with attention to detail in both the cup and the background."]}, {"id": 306, "type": "BizyAir_CLIPTextEncode", "pos": {"0": -752.07177734375, "1": 446}, "size": [371.80428583503794, 207.5518462621095], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 624, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [609, 611], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": [""]}, {"id": 135, "type": "EmptySD3LatentImage", "pos": {"0": 501.7686767578125, "1": 924.4931640625}, "size": [447.37248583503685, 138.00764626210912], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [622], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 311, "type": "BizyAir_KSampler", "pos": {"0": 903, "1": 209}, "size": [434.5654278350364, 443.3659252621095], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 616, "label": "model"}, {"name": "positive", "type": "BIZYAIR_CONDITIONING", "link": 615, "label": "positive"}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "link": 614, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": 622, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [620], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "BizyAir_KSampler"}, "widgets_values": [66155038679131, "fixed", 20, 4.5, "dpmpp_2m", "sgm_uniform", 1]}, {"id": 312, "type": "BizyAir_ModelSamplingSD3", "pos": {"0": 546, "1": 208}, "size": [336.2742508350368, 440.8228032621096], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 617, "label": "model"}], "outputs": [{"name": "BIZYAIR_MODEL", "type": "BIZYAIR_MODEL", "links": [616], "slot_index": 0, "shape": 3, "label": "BIZYAIR_MODEL"}], "properties": {"Node name for S&R": "BizyAir_ModelSamplingSD3"}, "widgets_values": [3]}, {"id": 308, "type": "BizyAir_ConditioningSetTimestepRange", "pos": {"0": 79, "1": 202}, "size": [411.5821468350374, 165.31709226210978], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "conditioning", "type": "BIZYAIR_CONDITIONING", "link": 610, "label": "conditioning"}], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [612], "slot_index": 0, "shape": 3, "label": "BIZYAIR_CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_ConditioningSetTimestepRange"}, "widgets_values": [0.1, 1]}, {"id": 307, "type": "BizyAir_ConditioningZeroOut", "pos": {"0": -334, "1": 216}, "size": [397.8690198350379, 168.9161652621098], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "conditioning", "type": "BIZYAIR_CONDITIONING", "link": 609, "label": "conditioning"}], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [610], "slot_index": 0, "shape": 3, "label": "BIZYAIR_CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_ConditioningZeroOut"}, "widgets_values": []}, {"id": 309, "type": "BizyAir_ConditioningSetTimestepRange", "pos": {"0": -345, "1": 441}, "size": [417.6708738350378, 193.65031526210953], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "conditioning", "type": "BIZYAIR_CONDITIONING", "link": 611, "label": "conditioning"}], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [613], "slot_index": 0, "shape": 3, "label": "BIZYAIR_CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_ConditioningSetTimestepRange"}, "widgets_values": [0, 0.1]}, {"id": 314, "type": "BizyAir_VAEDecode", "pos": {"0": 1365, "1": 204}, "size": [384.5049073090354, 445.58634605310954], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 620, "label": "samples"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 619, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [621], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAir_VAEDecode"}, "widgets_values": []}, {"id": 315, "type": "PreviewImage", "pos": {"0": 1795.1552734375, "1": 206.81385803222656}, "size": [442.1656938715346, 429.88840802088293], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 621, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 310, "type": "BizyAir_ConditioningCombine", "pos": {"0": 99, "1": 419}, "size": [404.8130975890364, 219.58656275310932], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "conditioning_1", "type": "BIZYAIR_CONDITIONING", "link": 612, "label": "conditioning_1"}, {"name": "conditioning_2", "type": "BIZYAIR_CONDITIONING", "link": 613, "label": "conditioning_2"}], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [614], "slot_index": 0, "shape": 3, "label": "BIZYAIR_CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_ConditioningCombine"}, "widgets_values": []}], "links": [[609, 306, 0, 307, 0, "BIZYAIR_CONDITIONING"], [610, 307, 0, 308, 0, "BIZYAIR_CONDITIONING"], [611, 306, 0, 309, 0, "BIZYAIR_CONDITIONING"], [612, 308, 0, 310, 0, "BIZYAIR_CONDITIONING"], [613, 309, 0, 310, 1, "BIZYAIR_CONDITIONING"], [614, 310, 0, 311, 2, "BIZYAIR_CONDITIONING"], [615, 304, 0, 311, 1, "BIZYAIR_CONDITIONING"], [616, 312, 0, 311, 0, "BIZYAIR_MODEL"], [617, 302, 0, 312, 0, "BIZYAIR_MODEL"], [619, 302, 2, 314, 1, "BIZYAIR_VAE"], [620, 311, 0, 314, 0, "LATENT"], [621, 314, 0, 315, 0, "IMAGE"], [622, 135, 0, 311, 3, "LATENT"], [624, 316, 0, 306, 0, "BIZYAIR_CLIP"], [625, 316, 0, 304, 0, "BIZYAIR_CLIP"], [627, 303, 0, 316, 0, "*"]], "groups": [{"title": "VAE", "bounding": [1347, 124, 417, 550], "color": "#b06634", "font_size": 24, "flags": {}}, {"title": "垫图", "bounding": [484, 845, 476, 238], "color": "#8A8", "font_size": 24, "flags": {}}, {"title": "反向提示词参与作图权重", "bounding": [-363, 125, 886, 550], "color": "#88A", "font_size": 24, "flags": {}}, {"title": "提示词", "bounding": [-772, 125, 409, 549], "color": "#a1309b", "font_size": 24, "flags": {}}, {"title": "模型", "bounding": [-1161, 125, 386, 549], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "采样器", "bounding": [524, 125, 822, 549], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"title": "预览图", "bounding": [1766, 124, 495, 548], "color": "#444", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.564473930053778, "offset": [572.617965700961, 290.0782754248898]}}, "version": 0.4}