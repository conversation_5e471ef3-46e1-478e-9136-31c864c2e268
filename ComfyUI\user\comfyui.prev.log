## ComfyUI-Manager: installing dependencies done.
[2025-07-24 22:40:42.412] ** ComfyUI startup time: 2025-07-24 22:40:42.412
[2025-07-24 22:40:42.412] ** Platform: Windows
[2025-07-24 22:40:42.412] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 22:40:42.415] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-24 22:40:42.415] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 22:40:42.415] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 22:40:42.415] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-24 22:40:42.415] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-24 22:40:42.415] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-24 22:40:46.310]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 22:40:46.310]    4.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 22:40:46.310]    8.7 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 22:40:46.310] 
[2025-07-24 22:40:50.927] Checkpoint files will always be loaded safely.
[2025-07-24 22:40:50.980] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-24 22:40:50.980] pytorch version: 2.4.1+cpu
[2025-07-24 22:40:50.980] Set vram state to: DISABLED
[2025-07-24 22:40:50.980] Device: cpu
[2025-07-24 22:40:53.719] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-24 22:40:53.777] torchaudio missing, ACE model will be broken
[2025-07-24 22:40:53.788] torchaudio missing, ACE model will be broken
[2025-07-24 22:41:00.119] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 22:41:00.119] ComfyUI version: 0.3.45
[2025-07-24 22:41:00.184] ComfyUI frontend version: 1.23.4
[2025-07-24 22:41:00.189] [Prompt Server] web root: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-07-24 22:41:02.123] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py", line 4, in <module>
    import torchaudio
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\__init__.py", line 2, in <module>
    from . import _extension  # noqa  # usort: skip
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\__init__.py", line 38, in <module>
    _load_lib("libtorchaudio")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\utils.py", line 60, in _load_lib
    torch.ops.load_library(path)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\_ops.py", line 1295, in load_library
    ctypes.CDLL(path)
  File "ctypes\__init__.py", line 379, in __init__
FileNotFoundError: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.

[2025-07-24 22:41:02.123] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py module for custom nodes: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
[2025-07-24 22:41:03.273] [92m[BizyAir][0m BizyAir ComfyUI Plugin: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 22:41:03.428] Display name '☁️BizyAir CogView4_6B_Pipe' might differ from the native display name.
[2025-07-24 22:41:03.439] Display name '☁️BizyAir SetUnionControlNetType' might differ from the native display name.
[2025-07-24 22:41:03.439] Display name '☁️BizyAir ControlNetInpaintingAliMamaApply' might differ from the native display name.
[2025-07-24 22:41:03.444] Display name '☁️BizyAir DisableNoise' might differ from the native display name.
[2025-07-24 22:41:03.444] Display name '☁️BizyAir VPScheduler' might differ from the native display name.
[2025-07-24 22:41:03.444] Display name '☁️BizyAir SplitSigmas' might differ from the native display name.
[2025-07-24 22:41:03.444] Display name '☁️BizyAir SplitSigmasDenoise' might differ from the native display name.
[2025-07-24 22:41:03.444] Display name '☁️BizyAir FlipSigmas' might differ from the native display name.
[2025-07-24 22:41:03.448] Display name '☁️BizyAir CFGGuider' might differ from the native display name.
[2025-07-24 22:41:03.450] Display name '☁️BizyAir TrainDatasetAdd' might differ from the native display name.
[2025-07-24 22:41:03.455] Display name '☁️BizyAir CLIPTextEncodeFlux' might differ from the native display name.
[2025-07-24 22:41:03.457] Display name '☁️BizyAir ReferenceLatent' might differ from the native display name.
[2025-07-24 22:41:03.457] Display name '☁️BizyAir FluxGuidance' might differ from the native display name.
[2025-07-24 22:41:03.459] Display name '☁️BizyAir Hy3D_2_1SimpleMeshGen' might differ from the native display name.
[2025-07-24 22:41:03.461] Display name '☁️BizyAir Hy3DExportMesh' might differ from the native display name.
[2025-07-24 22:41:03.465] Display name '☁️BizyAir InstructPixToPixConditioning' might differ from the native display name.
[2025-07-24 22:41:03.472] Display name '☁️BizyAir IPAdapterModelLoader' might differ from the native display name.
[2025-07-24 22:41:03.474] Display name '☁️BizyAir IPAdapterAdvanced' might differ from the native display name.
[2025-07-24 22:41:03.474] Display name '☁️BizyAir IPAdapterStyleComposition' might differ from the native display name.
[2025-07-24 22:41:03.476] Display name '☁️BizyAir JanusModelLoader' might differ from the native display name.
[2025-07-24 22:41:03.478] Display name '☁️BizyAir JanusImageUnderstanding' might differ from the native display name.
[2025-07-24 22:41:03.478] Display name '☁️BizyAir JanusImageGeneration' might differ from the native display name.
[2025-07-24 22:41:03.483] Display name '☁️BizyAir ModelSamplingSD3' might differ from the native display name.
[2025-07-24 22:41:03.485] Display name '☁️BizyAir ModelSamplingFlux' might differ from the native display name.
[2025-07-24 22:41:03.489] Display name '☁️BizyAir NunchakuPulidApply' might differ from the native display name.
[2025-07-24 22:41:03.490] Display name '☁️BizyAir NunchakuPulidLoader' might differ from the native display name.
[2025-07-24 22:41:03.492] Display name '☁️BizyAir TripleCLIPLoader' might differ from the native display name.
[2025-07-24 22:41:03.508] Display name '☁️BizyAir UltimateSDUpscale' might differ from the native display name.
[2025-07-24 22:41:03.517] Display name '☁️BizyAir Wan_LoraLoader' might differ from the native display name.
[2025-07-24 22:41:03.519] Display name '☁️BizyAir Wan_ImageToVideoPipeline' might differ from the native display name.
[2025-07-24 22:41:03.521] Display name '☁️BizyAir Wan_Model_Loader' might differ from the native display name.
[2025-07-24 22:41:03.522] Display name '☁️BizyAir Wan_T2V_Pipeline' might differ from the native display name.
[2025-07-24 22:41:03.538] Display name '☁️BizyAir LoraLoader_Legacy' might differ from the native display name.
[2025-07-24 22:41:03.538] Display name '☁️BizyAir ControlNetLoader_Legacy' might differ from the native display name.
[2025-07-24 22:41:03.540] Display name '☁️BizyAir SamplerCustomAdvanced' might differ from the native display name.
[2025-07-24 22:41:03.540] Display name '☁️BizyAir BasicGuider' might differ from the native display name.
[2025-07-24 22:41:03.542] Display name '☁️BizyAir BasicScheduler' might differ from the native display name.
[2025-07-24 22:41:03.542] Display name '☁️BizyAir DualCLIPLoader' might differ from the native display name.
[2025-07-24 22:41:03.542] Display name '☁️BizyAir KSamplerSelect' might differ from the native display name.
[2025-07-24 22:41:03.544] Display name '☁️BizyAir RandomNoise' might differ from the native display name.
[2025-07-24 22:41:03.544] Display name '☁️BizyAir InpaintModelConditioning' might differ from the native display name.
[2025-07-24 22:41:03.546] Display name '☁️BizyAir InpaintModelConditioning_v2' might differ from the native display name.
[2025-07-24 22:41:03.546] Display name '☁️BizyAir ConditioningZeroOut' might differ from the native display name.
[2025-07-24 22:41:03.548] Display name '☁️BizyAir ConditioningSetTimestepRange' might differ from the native display name.
[2025-07-24 22:41:03.548] Display name '☁️BizyAir PassParameter' might differ from the native display name.
[2025-07-24 22:41:05.411] 

[92m[BizyAir][0m Model hosting service initialized.

[2025-07-24 22:41:05.411] 
[2025-07-24 22:41:12.699] [34m[ComfyUI-Easy-Use] server: [0mv1.3.1 [92mLoaded[0m
[2025-07-24 22:41:12.699] [34m[ComfyUI-Easy-Use] web root: [0mC:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-07-24 22:41:12.813] ----------Jake Upgrade Nodes Loaded----------
[2025-07-24 22:41:12.852] ### Loading: ComfyUI-Manager (V3.34.1)
[2025-07-24 22:41:12.852] [ComfyUI-Manager] network_mode: public
[2025-07-24 22:41:13.124] ### ComfyUI Revision: 150 [9a470e07] *DETACHED | Released on '2025-07-21'
[2025-07-24 22:41:13.188] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-24 22:41:13.188] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-24 22:41:13.193] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-24 22:41:13.719] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-24 22:41:13.764] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-24 22:41:14.275] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\__init__.py", line 1, in <module>
    from .faceanalysis import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\faceanalysis.py", line 16, in <module>
    raise Exception("Please install either dlib or insightface to use this node.")
Exception: Please install either dlib or insightface to use this node.

[2025-07-24 22:41:14.275] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis module for custom nodes: Please install either dlib or insightface to use this node.
[2025-07-24 22:41:16.553] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-24 22:41:16.559] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-24 22:41:16.599] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-24 22:41:18.234] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-24 22:41:18.234] 
	[3m[93m"Art is not about making something perfect, it's about making something meaningful."[0m[3m - Unknown[0m
[2025-07-24 22:41:18.234] 
[2025-07-24 22:41:18.252] 
Import times for custom nodes:
[2025-07-24 22:41:18.252]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-24 22:41:18.252]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis
[2025-07-24 22:41:18.252]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-24 22:41:18.252]    0.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade
[2025-07-24 22:41:18.252]    0.3 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 22:41:18.252]    1.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-24 22:41:18.252]    2.2 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 22:41:18.252]    4.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns
[2025-07-24 22:41:18.252]    7.3 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 22:41:18.252] 
[2025-07-24 22:41:18.252] WARNING: some comfy_extras/ nodes did not import correctly. This may be because they are missing some dependencies.

[2025-07-24 22:41:18.261] IMPORT FAILED: nodes_audio.py
[2025-07-24 22:41:18.261] 
This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
[2025-07-24 22:41:18.261] Please do a: pip install -r requirements.txt
[2025-07-24 22:41:18.261] 
[2025-07-24 22:41:18.697] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-24 22:41:18.826] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-24 22:41:19.888] Context impl SQLiteImpl.
[2025-07-24 22:41:19.888] Will assume non-transactional DDL.
[2025-07-24 22:41:19.892] No target revision found.
[2025-07-24 22:41:19.967] Starting server

[2025-07-24 22:41:19.970] To see the GUI go to: http://127.0.0.1:8188
[2025-07-24 22:41:26.409] FETCH ComfyRegistry Data: 5/92
[2025-07-24 22:41:32.772] FETCH ComfyRegistry Data: 10/92
[2025-07-24 22:41:39.008] FETCH ComfyRegistry Data: 15/92
[2025-07-24 22:41:44.155] FETCH ComfyRegistry Data: 20/92
[2025-07-24 22:41:51.857] FETCH ComfyRegistry Data: 25/92
[2025-07-24 22:41:57.316] FETCH ComfyRegistry Data: 30/92
[2025-07-24 22:42:02.793] FETCH ComfyRegistry Data: 35/92
[2025-07-24 22:42:07.908] FETCH ComfyRegistry Data: 40/92
[2025-07-24 22:42:12.913] FETCH ComfyRegistry Data: 45/92
[2025-07-24 22:42:18.263] FETCH ComfyRegistry Data: 50/92
[2025-07-24 22:42:25.233] FETCH ComfyRegistry Data: 55/92
[2025-07-24 22:42:31.995] FETCH ComfyRegistry Data: 60/92
[2025-07-24 22:42:37.542] FETCH ComfyRegistry Data: 65/92
[2025-07-24 22:42:42.976] FETCH ComfyRegistry Data: 70/92
[2025-07-24 22:42:48.867] FETCH ComfyRegistry Data: 75/92
[2025-07-24 22:42:54.038] FETCH ComfyRegistry Data: 80/92
[2025-07-24 22:43:01.018] FETCH ComfyRegistry Data: 85/92
[2025-07-24 22:43:07.202] FETCH ComfyRegistry Data: 90/92
[2025-07-24 22:43:10.171] FETCH ComfyRegistry Data [DONE]
[2025-07-24 22:43:10.624] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-24 22:43:10.697] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-24 22:43:11.394] [ComfyUI-Manager] All startup tasks have been completed.
[2025-07-25 00:55:38.951] got prompt
[2025-07-25 00:55:38.951] invalid prompt: {'type': 'invalid_prompt', 'message': 'Cannot execute because a node is missing the class_type property.', 'details': "Node ID '#16'", 'extra_info': {}}
