## ComfyUI-Manager: installing dependencies done.
[2025-07-24 22:35:43.371] ** ComfyUI startup time: 2025-07-24 22:35:43.371
[2025-07-24 22:35:43.371] ** Platform: Windows
[2025-07-24 22:35:43.373] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 22:35:43.373] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-24 22:35:43.373] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 22:35:43.373] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 22:35:43.378] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-24 22:35:43.378] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-24 22:35:43.380] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-24 22:35:48.590]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 22:35:48.590]   12.4 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 22:35:48.591]   13.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 22:35:48.591] 
[2025-07-24 22:35:56.087] Checkpoint files will always be loaded safely.
[2025-07-24 22:35:56.197] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-24 22:35:56.197] pytorch version: 2.4.1+cpu
[2025-07-24 22:35:56.199] Set vram state to: DISABLED
[2025-07-24 22:35:56.201] Device: cpu
[2025-07-24 22:36:03.181] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-24 22:36:03.291] torchaudio missing, ACE model will be broken
[2025-07-24 22:36:03.306] torchaudio missing, ACE model will be broken
[2025-07-24 22:36:14.699] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 22:36:14.699] ComfyUI version: 0.3.45
[2025-07-24 22:36:14.800] ComfyUI frontend version: 1.23.4
[2025-07-24 22:36:14.806] [Prompt Server] web root: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-07-24 22:36:18.883] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py", line 4, in <module>
    import torchaudio
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\__init__.py", line 2, in <module>
    from . import _extension  # noqa  # usort: skip
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\__init__.py", line 38, in <module>
    _load_lib("libtorchaudio")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\utils.py", line 60, in _load_lib
    torch.ops.load_library(path)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\_ops.py", line 1295, in load_library
    ctypes.CDLL(path)
  File "ctypes\__init__.py", line 379, in __init__
FileNotFoundError: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.

[2025-07-24 22:36:18.883] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py module for custom nodes: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
[2025-07-24 22:36:20.118] [92m[BizyAir][0m BizyAir ComfyUI Plugin: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 22:36:20.350] Display name '☁️BizyAir CogView4_6B_Pipe' might differ from the native display name.
[2025-07-24 22:36:20.365] Display name '☁️BizyAir SetUnionControlNetType' might differ from the native display name.
[2025-07-24 22:36:20.365] Display name '☁️BizyAir ControlNetInpaintingAliMamaApply' might differ from the native display name.
[2025-07-24 22:36:20.368] Display name '☁️BizyAir DisableNoise' might differ from the native display name.
[2025-07-24 22:36:20.368] Display name '☁️BizyAir VPScheduler' might differ from the native display name.
[2025-07-24 22:36:20.368] Display name '☁️BizyAir SplitSigmas' might differ from the native display name.
[2025-07-24 22:36:20.368] Display name '☁️BizyAir SplitSigmasDenoise' might differ from the native display name.
[2025-07-24 22:36:20.372] Display name '☁️BizyAir FlipSigmas' might differ from the native display name.
[2025-07-24 22:36:20.372] Display name '☁️BizyAir CFGGuider' might differ from the native display name.
[2025-07-24 22:36:20.378] Display name '☁️BizyAir TrainDatasetAdd' might differ from the native display name.
[2025-07-24 22:36:20.384] Display name '☁️BizyAir CLIPTextEncodeFlux' might differ from the native display name.
[2025-07-24 22:36:20.384] Display name '☁️BizyAir ReferenceLatent' might differ from the native display name.
[2025-07-24 22:36:20.384] Display name '☁️BizyAir FluxGuidance' might differ from the native display name.
[2025-07-24 22:36:20.386] Display name '☁️BizyAir Hy3D_2_1SimpleMeshGen' might differ from the native display name.
[2025-07-24 22:36:20.386] Display name '☁️BizyAir Hy3DExportMesh' might differ from the native display name.
[2025-07-24 22:36:20.398] Display name '☁️BizyAir InstructPixToPixConditioning' might differ from the native display name.
[2025-07-24 22:36:20.404] Display name '☁️BizyAir IPAdapterModelLoader' might differ from the native display name.
[2025-07-24 22:36:20.405] Display name '☁️BizyAir IPAdapterAdvanced' might differ from the native display name.
[2025-07-24 22:36:20.405] Display name '☁️BizyAir IPAdapterStyleComposition' might differ from the native display name.
[2025-07-24 22:36:20.409] Display name '☁️BizyAir JanusModelLoader' might differ from the native display name.
[2025-07-24 22:36:20.411] Display name '☁️BizyAir JanusImageUnderstanding' might differ from the native display name.
[2025-07-24 22:36:20.411] Display name '☁️BizyAir JanusImageGeneration' might differ from the native display name.
[2025-07-24 22:36:20.418] Display name '☁️BizyAir ModelSamplingSD3' might differ from the native display name.
[2025-07-24 22:36:20.418] Display name '☁️BizyAir ModelSamplingFlux' might differ from the native display name.
[2025-07-24 22:36:20.418] Display name '☁️BizyAir NunchakuPulidApply' might differ from the native display name.
[2025-07-24 22:36:20.418] Display name '☁️BizyAir NunchakuPulidLoader' might differ from the native display name.
[2025-07-24 22:36:20.433] Display name '☁️BizyAir TripleCLIPLoader' might differ from the native display name.
[2025-07-24 22:36:20.449] Display name '☁️BizyAir UltimateSDUpscale' might differ from the native display name.
[2025-07-24 22:36:20.459] Display name '☁️BizyAir Wan_LoraLoader' might differ from the native display name.
[2025-07-24 22:36:20.460] Display name '☁️BizyAir Wan_ImageToVideoPipeline' might differ from the native display name.
[2025-07-24 22:36:20.462] Display name '☁️BizyAir Wan_Model_Loader' might differ from the native display name.
[2025-07-24 22:36:20.462] Display name '☁️BizyAir Wan_T2V_Pipeline' might differ from the native display name.
[2025-07-24 22:36:20.486] Display name '☁️BizyAir LoraLoader_Legacy' might differ from the native display name.
[2025-07-24 22:36:20.488] Display name '☁️BizyAir ControlNetLoader_Legacy' might differ from the native display name.
[2025-07-24 22:36:20.488] Display name '☁️BizyAir SamplerCustomAdvanced' might differ from the native display name.
[2025-07-24 22:36:20.488] Display name '☁️BizyAir BasicGuider' might differ from the native display name.
[2025-07-24 22:36:20.488] Display name '☁️BizyAir BasicScheduler' might differ from the native display name.
[2025-07-24 22:36:20.491] Display name '☁️BizyAir DualCLIPLoader' might differ from the native display name.
[2025-07-24 22:36:20.491] Display name '☁️BizyAir KSamplerSelect' might differ from the native display name.
[2025-07-24 22:36:20.493] Display name '☁️BizyAir RandomNoise' might differ from the native display name.
[2025-07-24 22:36:20.493] Display name '☁️BizyAir InpaintModelConditioning' might differ from the native display name.
[2025-07-24 22:36:20.493] Display name '☁️BizyAir InpaintModelConditioning_v2' might differ from the native display name.
[2025-07-24 22:36:20.495] Display name '☁️BizyAir ConditioningZeroOut' might differ from the native display name.
[2025-07-24 22:36:20.495] Display name '☁️BizyAir ConditioningSetTimestepRange' might differ from the native display name.
[2025-07-24 22:36:20.495] Display name '☁️BizyAir PassParameter' might differ from the native display name.
[2025-07-24 22:36:22.822] 

[92m[BizyAir][0m Model hosting service initialized.

[2025-07-24 22:36:22.822] 
[2025-07-24 22:36:35.507] [34m[ComfyUI-Easy-Use] server: [0mv1.3.1 [92mLoaded[0m
[2025-07-24 22:36:35.507] [34m[ComfyUI-Easy-Use] web root: [0mC:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-07-24 22:36:35.639] ----------Jake Upgrade Nodes Loaded----------
[2025-07-24 22:36:35.707] ### Loading: ComfyUI-Manager (V3.34.1)
[2025-07-24 22:36:35.707] [ComfyUI-Manager] network_mode: public
[2025-07-24 22:36:35.963] ### ComfyUI Revision: 150 [9a470e07] *DETACHED | Released on '2025-07-21'
[2025-07-24 22:36:36.200] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-24 22:36:36.201] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-24 22:36:36.205] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-24 22:36:36.638] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-24 22:36:36.917] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-24 22:36:37.053] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-24 22:36:38.134] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-24 22:36:38.575] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-07-24 22:36:38.638] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-24 22:36:38.807] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\__init__.py", line 1, in <module>
    from .faceanalysis import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\faceanalysis.py", line 16, in <module>
    raise Exception("Please install either dlib or insightface to use this node.")
Exception: Please install either dlib or insightface to use this node.

[2025-07-24 22:36:38.807] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis module for custom nodes: Please install either dlib or insightface to use this node.
[2025-07-24 22:36:42.681] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-24 22:36:42.681] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-24 22:36:44.488] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-24 22:36:44.488] 
	[3m[93m"Success is not final, failure is not fatal: It is the courage to continue that counts."[0m[3m - Winston Churchill[0m
[2025-07-24 22:36:44.488] 
[2025-07-24 22:36:44.515] 
Import times for custom nodes:
[2025-07-24 22:36:44.515]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-24 22:36:44.518]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-24 22:36:44.519]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis
[2025-07-24 22:36:44.519]    0.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade
[2025-07-24 22:36:44.519]    0.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 22:36:44.520]    2.6 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-24 22:36:44.521]    2.8 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 22:36:44.522]    5.7 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns
[2025-07-24 22:36:44.522]   12.7 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 22:36:44.522] 
[2025-07-24 22:36:44.523] WARNING: some comfy_extras/ nodes did not import correctly. This may be because they are missing some dependencies.

[2025-07-24 22:36:44.523] IMPORT FAILED: nodes_audio.py
[2025-07-24 22:36:44.523] 
This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
[2025-07-24 22:36:44.523] Please run the update script: update/update_comfyui.bat
[2025-07-24 22:36:44.523] 
[2025-07-24 22:36:46.174] Context impl SQLiteImpl.
[2025-07-24 22:36:46.177] Will assume non-transactional DDL.
[2025-07-24 22:36:46.183] No target revision found.
[2025-07-24 22:36:46.252] Starting server

[2025-07-24 22:36:46.252] To see the GUI go to: http://127.0.0.1:8188
[2025-07-24 22:36:48.534] FETCH ComfyRegistry Data: 5/92
[2025-07-24 22:36:54.538] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\bizyengine\core\common\client.py:69: UserWarning: invalid api_key
  warnings.warn("invalid api_key")
[2025-07-24 22:37:00.378] FETCH ComfyRegistry Data: 10/92
[2025-07-24 22:37:03.317] Traceback (most recent call last):
[2025-07-24 22:37:03.317]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager\glob\manager_server.py", line 419, in do_install
    res = await core.unified_manager.install_by_id(node_name, version_spec, channel, mode, return_postinstall=skip_post_install)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-24 22:37:03.319]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager\glob\manager_core.py", line 1547, in install_by_id
    res = self.cnr_install(node_id, version_spec, instant_execution=instant_execution, no_deps=no_deps, return_postinstall=return_postinstall)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-24 22:37:03.322]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager\glob\manager_core.py", line 1315, in cnr_install
    manager_downloader.download_url(node_info.download_url, get_default_custom_nodes_path(), archive_name)
[2025-07-24 22:37:03.325]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager\glob\manager_downloader.py", line 58, in download_url
    return torchvision_download_url(model_url, model_dir, filename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-24 22:37:03.325]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchvision\datasets\utils.py", line 122, in download_url
    url = _get_redirect_url(url, max_hops=max_redirect_hops)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-24 22:37:03.329]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchvision\datasets\utils.py", line 66, in _get_redirect_url
    with urllib.request.urlopen(urllib.request.Request(url, headers=headers)) as response:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-24 22:37:03.331]   File "urllib\request.py", line 215, in urlopen
[2025-07-24 22:37:03.333]   File "urllib\request.py", line 521, in open
[2025-07-24 22:37:03.335]   File "urllib\request.py", line 630, in http_response
[2025-07-24 22:37:03.335]   File "urllib\request.py", line 559, in error
[2025-07-24 22:37:03.337]   File "urllib\request.py", line 492, in _call_chain
[2025-07-24 22:37:03.337]   File "urllib\request.py", line 639, in http_error_default
[2025-07-24 22:37:03.342] urllib.error.HTTPError: HTTP Error 404: Not Found
[2025-07-24 22:37:03.346] 
[ComfyUI-Manager] Queued works are completed.
{'install': 1}
[2025-07-24 22:37:03.354] 
After restarting ComfyUI, please refresh the browser.
[2025-07-24 22:37:05.549] FETCH ComfyRegistry Data: 15/92
[2025-07-24 22:37:11.166] FETCH ComfyRegistry Data: 20/92
[2025-07-24 22:37:19.675] FETCH ComfyRegistry Data: 25/92
[2025-07-24 22:37:25.793] FETCH ComfyRegistry Data: 30/92
[2025-07-24 22:37:32.312] FETCH ComfyRegistry Data: 35/92
[2025-07-24 22:37:41.967] FETCH ComfyRegistry Data: 40/92
[2025-07-24 22:37:48.829] FETCH ComfyRegistry Data: 45/92
[2025-07-24 22:37:56.142] FETCH ComfyRegistry Data: 50/92
[2025-07-24 22:38:07.516] FETCH ComfyRegistry Data: 55/92
[2025-07-24 22:38:16.748] FETCH ComfyRegistry Data: 60/92
[2025-07-24 22:38:27.220] FETCH ComfyRegistry Data: 65/92
[2025-07-24 22:38:33.928] FETCH ComfyRegistry Data: 70/92
[2025-07-24 22:38:41.763] FETCH ComfyRegistry Data: 75/92
[2025-07-24 22:38:47.548] FETCH ComfyRegistry Data: 80/92
[2025-07-24 22:38:55.253] FETCH ComfyRegistry Data: 85/92
[2025-07-24 22:39:11.385] FETCH ComfyRegistry Data: 90/92
[2025-07-24 22:39:14.758] FETCH ComfyRegistry Data [DONE]
[2025-07-24 22:39:15.211] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-24 22:39:15.295] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-24 22:39:17.412] [ComfyUI-Manager] All startup tasks have been completed.
