## ComfyUI-Manager: installing dependencies done.
[2025-07-25 10:00:52.872] ** ComfyUI startup time: 2025-07-25 10:00:52.872
[2025-07-25 10:00:52.874] ** Platform: Windows
[2025-07-25 10:00:52.874] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-25 10:00:52.876] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-25 10:00:52.880] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-25 10:00:52.882] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-25 10:00:52.889] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-25 10:00:52.889] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-25 10:00:52.893] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-25 10:00:56.135]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-25 10:00:56.135]    7.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-25 10:00:56.135]    8.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-25 10:00:56.135] 
[2025-07-25 10:01:02.686] Checkpoint files will always be loaded safely.
[2025-07-25 10:01:02.767] Traceback (most recent call last):
[2025-07-25 10:01:02.767]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 138, in <module>
[2025-07-25 10:01:02.770]     import execution
[2025-07-25 10:01:02.770]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 15, in <module>
[2025-07-25 10:01:02.789]     import comfy.model_management
[2025-07-25 10:01:02.790]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\model_management.py", line 221, in <module>
[2025-07-25 10:01:02.807]     total_vram = get_total_memory(get_torch_device()) / (1024 * 1024)
[2025-07-25 10:01:02.807]                                   ^^^^^^^^^^^^^^^^^^
[2025-07-25 10:01:02.821]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\model_management.py", line 172, in get_torch_device
[2025-07-25 10:01:02.821]     return torch.device(torch.cuda.current_device())
[2025-07-25 10:01:02.828]                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-25 10:01:02.843]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\cuda\__init__.py", line 878, in current_device
[2025-07-25 10:01:02.845]     _lazy_init()
[2025-07-25 10:01:02.846]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\cuda\__init__.py", line 305, in _lazy_init
[2025-07-25 10:01:02.847]     raise AssertionError("Torch not compiled with CUDA enabled")
[2025-07-25 10:01:02.850] AssertionError: Torch not compiled with CUDA enabled
