## ComfyUI-Manager: installing dependencies done.
[2025-07-23 21:58:29.448] ** ComfyUI startup time: 2025-07-23 21:58:29.448
[2025-07-23 21:58:29.448] ** Platform: Windows
[2025-07-23 21:58:29.448] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-23 21:58:29.448] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-23 21:58:29.448] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-23 21:58:29.448] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-23 21:58:29.459] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-23 21:58:29.460] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-23 21:58:29.460] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-23 21:58:33.313]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-23 21:58:33.313]    8.9 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-23 21:58:33.313] 
[2025-07-23 21:58:38.339] Checkpoint files will always be loaded safely.
[2025-07-23 21:58:38.392] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-23 21:58:38.392] pytorch version: 2.4.1+cpu
[2025-07-23 21:58:38.397] Set vram state to: DISABLED
[2025-07-23 21:58:38.397] Device: cpu
[2025-07-23 21:58:42.648] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-23 21:58:42.768] torchaudio missing, ACE model will be broken
[2025-07-23 21:58:42.803] torchaudio missing, ACE model will be broken
[2025-07-23 21:58:51.013] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-23 21:58:51.013] ComfyUI version: 0.3.45
[2025-07-23 21:58:51.085] ComfyUI frontend version: 1.23.4
[2025-07-23 21:58:51.090] [Prompt Server] web root: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-07-23 21:58:53.910] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py", line 4, in <module>
    import torchaudio
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\__init__.py", line 2, in <module>
    from . import _extension  # noqa  # usort: skip
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\__init__.py", line 38, in <module>
    _load_lib("libtorchaudio")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\utils.py", line 60, in _load_lib
    torch.ops.load_library(path)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\_ops.py", line 1295, in load_library
    ctypes.CDLL(path)
  File "ctypes\__init__.py", line 379, in __init__
FileNotFoundError: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.

[2025-07-23 21:58:53.912] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py module for custom nodes: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
[2025-07-23 21:59:04.268] [34m[ComfyUI-Easy-Use] server: [0mv1.3.1 [92mLoaded[0m
[2025-07-23 21:59:04.273] [34m[ComfyUI-Easy-Use] web root: [0mC:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-07-23 21:59:04.549] ----------Jake Upgrade Nodes Loaded----------
[2025-07-23 21:59:04.692] ### Loading: ComfyUI-Manager (V3.34.1)
[2025-07-23 21:59:04.735] [ComfyUI-Manager] network_mode: public
[2025-07-23 21:59:05.733] ### ComfyUI Revision: 150 [9a470e07] *DETACHED | Released on '2025-07-21'
[2025-07-23 21:59:05.920] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-23 21:59:06.022] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-23 21:59:06.091] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-23 21:59:07.460] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-23 21:59:07.519] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-23 21:59:07.963] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-23 21:59:08.391] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-23 21:59:08.534] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-07-23 21:59:08.587] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-23 21:59:08.729] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\__init__.py", line 1, in <module>
    from .faceanalysis import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\faceanalysis.py", line 16, in <module>
    raise Exception("Please install either dlib or insightface to use this node.")
Exception: Please install either dlib or insightface to use this node.

[2025-07-23 21:59:08.731] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis module for custom nodes: Please install either dlib or insightface to use this node.
[2025-07-23 21:59:11.964] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-23 21:59:11.969] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-23 21:59:13.681] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-23 21:59:13.681] 
	[3m[93m"Art is the lie that enables us to realize the truth."[0m[3m - Pablo Picasso[0m
[2025-07-23 21:59:13.706] 
[2025-07-23 21:59:13.733] 
Import times for custom nodes:
[2025-07-23 21:59:13.735]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-23 21:59:13.735]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis
[2025-07-23 21:59:13.737]    0.3 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade
[2025-07-23 21:59:13.737]    0.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-23 21:59:13.738]    1.3 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-23 21:59:13.738]    2.8 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-23 21:59:13.739]    4.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns
[2025-07-23 21:59:13.739]    9.2 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-23 21:59:13.739] 
[2025-07-23 21:59:13.740] WARNING: some comfy_extras/ nodes did not import correctly. This may be because they are missing some dependencies.

[2025-07-23 21:59:13.740] IMPORT FAILED: nodes_audio.py
[2025-07-23 21:59:13.740] 
This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
[2025-07-23 21:59:13.740] Please run the update script: update/update_comfyui.bat
[2025-07-23 21:59:13.740] 
[2025-07-23 21:59:14.053] FETCH ComfyRegistry Data: 5/92
[2025-07-23 21:59:15.100] Context impl SQLiteImpl.
[2025-07-23 21:59:15.100] Will assume non-transactional DDL.
[2025-07-23 21:59:15.103] No target revision found.
[2025-07-23 21:59:15.159] Starting server

[2025-07-23 21:59:15.159] To see the GUI go to: http://127.0.0.1:8188
[2025-07-23 21:59:20.854] FETCH ComfyRegistry Data: 10/92
[2025-07-23 21:59:21.989] got prompt
[2025-07-23 21:59:22.524] model weight dtype torch.float32, manual cast: None
[2025-07-23 21:59:22.548] model_type EPS
[2025-07-23 21:59:25.086] Using split attention in VAE
[2025-07-23 21:59:25.093] Using split attention in VAE
[2025-07-23 21:59:25.486] VAE load device: cpu, offload device: cpu, dtype: torch.float32
[2025-07-23 21:59:26.209] Requested to load SD1ClipModel
[2025-07-23 21:59:26.226] loaded completely 9.5367431640625e+25 235.84423828125 True
[2025-07-23 21:59:26.230] CLIP/text encoder model load device: cpu, offload device: cpu, current: cpu, dtype: torch.float16
[2025-07-23 21:59:26.697] FETCH ComfyRegistry Data: 15/92
[2025-07-23 21:59:27.726] Requested to load AutoencoderKL
[2025-07-23 21:59:27.742] loaded completely 9.5367431640625e+25 319.11416244506836 True
[2025-07-23 21:59:32.673] FETCH ComfyRegistry Data: 20/92
[2025-07-23 21:59:38.483] FETCH ComfyRegistry Data: 25/92
[2025-07-23 21:59:44.152] FETCH ComfyRegistry Data: 30/92
[2025-07-23 21:59:48.189] !!! Exception during processing !!! IPAdapterSimple.apply_ipadapter() got an unexpected keyword argument 'clip_vision'
[2025-07-23 21:59:48.198] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
TypeError: IPAdapterSimple.apply_ipadapter() got an unexpected keyword argument 'clip_vision'

[2025-07-23 21:59:48.207] Prompt executed in 26.21 seconds
[2025-07-23 21:59:50.965] FETCH ComfyRegistry Data: 35/92
[2025-07-23 21:59:56.014] FETCH ComfyRegistry Data: 40/92
[2025-07-23 22:00:01.104] FETCH ComfyRegistry Data: 45/92
[2025-07-23 22:00:04.851] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json [DONE]
[2025-07-23 22:00:06.302] FETCH ComfyRegistry Data: 50/92
[2025-07-23 22:00:11.593] FETCH ComfyRegistry Data: 55/92
[2025-07-23 22:00:17.095] FETCH ComfyRegistry Data: 60/92
[2025-07-23 22:00:23.912] FETCH ComfyRegistry Data: 65/92
[2025-07-23 22:00:26.309] got prompt
[2025-07-23 22:00:26.882] !!! Exception during processing !!! IPAdapterSimple.apply_ipadapter() got an unexpected keyword argument 'clip_vision'
[2025-07-23 22:00:26.889] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
TypeError: IPAdapterSimple.apply_ipadapter() got an unexpected keyword argument 'clip_vision'

[2025-07-23 22:00:26.896] Prompt executed in 0.57 seconds
[2025-07-23 22:00:29.294] FETCH ComfyRegistry Data: 70/92
[2025-07-23 22:00:35.294] FETCH ComfyRegistry Data: 75/92
[2025-07-23 22:00:40.772] FETCH ComfyRegistry Data: 80/92
[2025-07-23 22:00:46.285] FETCH ComfyRegistry Data: 85/92
[2025-07-23 22:00:54.165] FETCH ComfyRegistry Data: 90/92
[2025-07-23 22:00:56.581] FETCH ComfyRegistry Data [DONE]
[2025-07-23 22:00:57.154] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-23 22:00:57.253] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-23 22:00:57.913] [ComfyUI-Manager] All startup tasks have been completed.
[2025-07-23 22:15:13.340] got prompt
[2025-07-23 22:15:13.663] got prompt
[2025-07-23 22:15:14.032] !!! Exception during processing !!! IPAdapterSimple.apply_ipadapter() got an unexpected keyword argument 'clip_vision'
[2025-07-23 22:15:14.042] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
TypeError: IPAdapterSimple.apply_ipadapter() got an unexpected keyword argument 'clip_vision'

[2025-07-23 22:15:14.049] Prompt executed in 0.64 seconds
[2025-07-23 22:15:16.832] !!! Exception during processing !!! IPAdapterSimple.apply_ipadapter() got an unexpected keyword argument 'clip_vision'
[2025-07-23 22:15:16.854] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
TypeError: IPAdapterSimple.apply_ipadapter() got an unexpected keyword argument 'clip_vision'

[2025-07-23 22:15:16.859] Prompt executed in 0.48 seconds
[2025-07-23 22:17:06.075] got prompt
[2025-07-23 22:17:06.751] !!! Exception during processing !!! IPAdapterSimple.apply_ipadapter() got an unexpected keyword argument 'clip_vision'
[2025-07-23 22:17:06.758] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
TypeError: IPAdapterSimple.apply_ipadapter() got an unexpected keyword argument 'clip_vision'

[2025-07-23 22:17:06.758] Prompt executed in 0.68 seconds
