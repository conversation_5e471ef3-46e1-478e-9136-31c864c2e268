## ComfyUI-Manager: installing dependencies done.
[2025-07-24 14:30:00.760] ** ComfyUI startup time: 2025-07-24 14:30:00.760
[2025-07-24 14:30:00.764] ** Platform: Windows
[2025-07-24 14:30:00.764] ** Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-24 14:30:00.765] ** Python executable: C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\python.exe
[2025-07-24 14:30:00.765] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 14:30:00.765] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 14:30:00.765] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-24 14:30:00.765] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-24 14:30:00.767] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-24 14:30:03.719]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 14:30:03.719]    2.6 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 14:30:03.719]    5.9 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 14:30:03.720] 
[2025-07-24 14:30:08.684] Checkpoint files will always be loaded safely.
[2025-07-24 14:30:08.719] Traceback (most recent call last):
[2025-07-24 14:30:08.719]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 138, in <module>
[2025-07-24 14:30:08.719]     import execution
[2025-07-24 14:30:08.719]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 15, in <module>
[2025-07-24 14:30:08.719]     import comfy.model_management
[2025-07-24 14:30:08.724]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\model_management.py", line 221, in <module>
[2025-07-24 14:30:08.724]     total_vram = get_total_memory(get_torch_device()) / (1024 * 1024)
[2025-07-24 14:30:08.724]                                   ^^^^^^^^^^^^^^^^^^
[2025-07-24 14:30:08.738]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\model_management.py", line 172, in get_torch_device
[2025-07-24 14:30:08.739]     return torch.device(torch.cuda.current_device())
[2025-07-24 14:30:08.740]                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-24 14:30:08.753]   File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\torch\cuda\__init__.py", line 1026, in current_device
[2025-07-24 14:30:08.754]     _lazy_init()
[2025-07-24 14:30:08.755]   File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\torch\cuda\__init__.py", line 372, in _lazy_init
[2025-07-24 14:30:08.756]     torch._C._cuda_init()
[2025-07-24 14:30:08.757] RuntimeError: Found no NVIDIA driver on your system. Please check that you have an NVIDIA GPU and installed a driver from http://www.nvidia.com/Download/index.aspx
