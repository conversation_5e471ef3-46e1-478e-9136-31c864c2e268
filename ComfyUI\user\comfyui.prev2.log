## ComfyUI-Manager: installing dependencies done.
[2025-07-24 14:42:52.847] ** ComfyUI startup time: 2025-07-24 14:42:52.847
[2025-07-24 14:42:52.847] ** Platform: Windows
[2025-07-24 14:42:52.848] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 14:42:52.848] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-24 14:42:52.849] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 14:42:52.849] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 14:42:52.852] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-24 14:42:52.852] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-24 14:42:52.853] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-24 14:42:54.830]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 14:42:54.830]    2.3 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 14:42:54.830]    3.9 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 14:42:54.830] 
[2025-07-24 14:42:57.179] Checkpoint files will always be loaded safely.
[2025-07-24 14:42:57.207] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-24 14:42:57.207] pytorch version: 2.4.1+cpu
[2025-07-24 14:42:57.208] Set vram state to: DISABLED
[2025-07-24 14:42:57.210] Device: cpu
[2025-07-24 14:42:59.150] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-24 14:42:59.191] torchaudio missing, ACE model will be broken
[2025-07-24 14:42:59.198] torchaudio missing, ACE model will be broken
[2025-07-24 14:43:02.115] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 14:43:02.115] ComfyUI version: 0.3.45
[2025-07-24 14:43:02.151] ComfyUI frontend version: 1.23.4
[2025-07-24 14:43:02.153] [Prompt Server] web root: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-07-24 14:43:03.380] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py", line 4, in <module>
    import torchaudio
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\__init__.py", line 2, in <module>
    from . import _extension  # noqa  # usort: skip
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\__init__.py", line 38, in <module>
    _load_lib("libtorchaudio")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\utils.py", line 60, in _load_lib
    torch.ops.load_library(path)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\_ops.py", line 1295, in load_library
    ctypes.CDLL(path)
  File "ctypes\__init__.py", line 379, in __init__
FileNotFoundError: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.

[2025-07-24 14:43:03.380] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py module for custom nodes: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
[2025-07-24 14:43:03.897] [92m[BizyAir][0m BizyAir ComfyUI Plugin: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 14:43:03.976] Display name '☁️BizyAir CogView4_6B_Pipe' might differ from the native display name.
[2025-07-24 14:43:03.982] Display name '☁️BizyAir SetUnionControlNetType' might differ from the native display name.
[2025-07-24 14:43:03.982] Display name '☁️BizyAir ControlNetInpaintingAliMamaApply' might differ from the native display name.
[2025-07-24 14:43:03.985] Display name '☁️BizyAir DisableNoise' might differ from the native display name.
[2025-07-24 14:43:03.985] Display name '☁️BizyAir VPScheduler' might differ from the native display name.
[2025-07-24 14:43:03.985] Display name '☁️BizyAir SplitSigmas' might differ from the native display name.
[2025-07-24 14:43:03.985] Display name '☁️BizyAir SplitSigmasDenoise' might differ from the native display name.
[2025-07-24 14:43:03.986] Display name '☁️BizyAir FlipSigmas' might differ from the native display name.
[2025-07-24 14:43:03.986] Display name '☁️BizyAir CFGGuider' might differ from the native display name.
[2025-07-24 14:43:03.988] Display name '☁️BizyAir TrainDatasetAdd' might differ from the native display name.
[2025-07-24 14:43:03.991] Display name '☁️BizyAir CLIPTextEncodeFlux' might differ from the native display name.
[2025-07-24 14:43:03.992] Display name '☁️BizyAir ReferenceLatent' might differ from the native display name.
[2025-07-24 14:43:03.992] Display name '☁️BizyAir FluxGuidance' might differ from the native display name.
[2025-07-24 14:43:03.994] Display name '☁️BizyAir Hy3D_2_1SimpleMeshGen' might differ from the native display name.
[2025-07-24 14:43:03.994] Display name '☁️BizyAir Hy3DExportMesh' might differ from the native display name.
[2025-07-24 14:43:03.997] Display name '☁️BizyAir InstructPixToPixConditioning' might differ from the native display name.
[2025-07-24 14:43:04.000] Display name '☁️BizyAir IPAdapterModelLoader' might differ from the native display name.
[2025-07-24 14:43:04.001] Display name '☁️BizyAir IPAdapterAdvanced' might differ from the native display name.
[2025-07-24 14:43:04.001] Display name '☁️BizyAir IPAdapterStyleComposition' might differ from the native display name.
[2025-07-24 14:43:04.002] Display name '☁️BizyAir JanusModelLoader' might differ from the native display name.
[2025-07-24 14:43:04.003] Display name '☁️BizyAir JanusImageUnderstanding' might differ from the native display name.
[2025-07-24 14:43:04.003] Display name '☁️BizyAir JanusImageGeneration' might differ from the native display name.
[2025-07-24 14:43:04.006] Display name '☁️BizyAir ModelSamplingSD3' might differ from the native display name.
[2025-07-24 14:43:04.006] Display name '☁️BizyAir ModelSamplingFlux' might differ from the native display name.
[2025-07-24 14:43:04.010] Display name '☁️BizyAir NunchakuPulidApply' might differ from the native display name.
[2025-07-24 14:43:04.010] Display name '☁️BizyAir NunchakuPulidLoader' might differ from the native display name.
[2025-07-24 14:43:04.014] Display name '☁️BizyAir TripleCLIPLoader' might differ from the native display name.
[2025-07-24 14:43:04.022] Display name '☁️BizyAir UltimateSDUpscale' might differ from the native display name.
[2025-07-24 14:43:04.025] Display name '☁️BizyAir Wan_LoraLoader' might differ from the native display name.
[2025-07-24 14:43:04.025] Display name '☁️BizyAir Wan_ImageToVideoPipeline' might differ from the native display name.
[2025-07-24 14:43:04.029] Display name '☁️BizyAir Wan_Model_Loader' might differ from the native display name.
[2025-07-24 14:43:04.029] Display name '☁️BizyAir Wan_T2V_Pipeline' might differ from the native display name.
[2025-07-24 14:43:04.040] Display name '☁️BizyAir LoraLoader_Legacy' might differ from the native display name.
[2025-07-24 14:43:04.040] Display name '☁️BizyAir ControlNetLoader_Legacy' might differ from the native display name.
[2025-07-24 14:43:04.041] Display name '☁️BizyAir SamplerCustomAdvanced' might differ from the native display name.
[2025-07-24 14:43:04.042] Display name '☁️BizyAir BasicGuider' might differ from the native display name.
[2025-07-24 14:43:04.042] Display name '☁️BizyAir BasicScheduler' might differ from the native display name.
[2025-07-24 14:43:04.043] Display name '☁️BizyAir DualCLIPLoader' might differ from the native display name.
[2025-07-24 14:43:04.043] Display name '☁️BizyAir KSamplerSelect' might differ from the native display name.
[2025-07-24 14:43:04.043] Display name '☁️BizyAir RandomNoise' might differ from the native display name.
[2025-07-24 14:43:04.043] Display name '☁️BizyAir InpaintModelConditioning' might differ from the native display name.
[2025-07-24 14:43:04.044] Display name '☁️BizyAir InpaintModelConditioning_v2' might differ from the native display name.
[2025-07-24 14:43:04.045] Display name '☁️BizyAir ConditioningZeroOut' might differ from the native display name.
[2025-07-24 14:43:04.045] Display name '☁️BizyAir ConditioningSetTimestepRange' might differ from the native display name.
[2025-07-24 14:43:04.045] Display name '☁️BizyAir PassParameter' might differ from the native display name.
[2025-07-24 14:43:05.008] 

[92m[BizyAir][0m Model hosting service initialized.

[2025-07-24 14:43:05.008] 
[2025-07-24 14:43:08.722] [34m[ComfyUI-Easy-Use] server: [0mv1.3.1 [92mLoaded[0m
[2025-07-24 14:43:08.722] [34m[ComfyUI-Easy-Use] web root: [0mC:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-07-24 14:43:08.778] ----------Jake Upgrade Nodes Loaded----------
[2025-07-24 14:43:08.794] ### Loading: ComfyUI-Manager (V3.34.1)
[2025-07-24 14:43:08.794] [ComfyUI-Manager] network_mode: public
[2025-07-24 14:43:08.937] ### ComfyUI Revision: 150 [9a470e07] *DETACHED | Released on '2025-07-21'
[2025-07-24 14:43:08.970] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-24 14:43:08.974] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-24 14:43:08.975] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-24 14:43:09.022] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-24 14:43:09.076] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-24 14:43:09.127] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-24 14:43:09.199] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-24 14:43:09.283] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-24 14:43:09.925] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-07-24 14:43:09.981] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\__init__.py", line 1, in <module>
    from .faceanalysis import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\faceanalysis.py", line 16, in <module>
    raise Exception("Please install either dlib or insightface to use this node.")
Exception: Please install either dlib or insightface to use this node.

[2025-07-24 14:43:09.987] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis module for custom nodes: Please install either dlib or insightface to use this node.
[2025-07-24 14:43:11.249] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-24 14:43:11.253] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-24 14:43:12.069] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-24 14:43:12.069] 
	[3m[93m"Don't let yesterday take up too much of today."[0m[3m - Will Rogers[0m
[2025-07-24 14:43:12.069] 
[2025-07-24 14:43:12.077] 
Import times for custom nodes:
[2025-07-24 14:43:12.077]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-24 14:43:12.077]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis
[2025-07-24 14:43:12.077]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-24 14:43:12.077]    0.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade
[2025-07-24 14:43:12.082]    0.2 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 14:43:12.082]    1.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-24 14:43:12.082]    1.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 14:43:12.082]    2.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns
[2025-07-24 14:43:12.083]    3.7 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 14:43:12.083] 
[2025-07-24 14:43:12.083] WARNING: some comfy_extras/ nodes did not import correctly. This may be because they are missing some dependencies.

[2025-07-24 14:43:12.083] IMPORT FAILED: nodes_audio.py
[2025-07-24 14:43:12.083] 
This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
[2025-07-24 14:43:12.083] Please run the update script: update/update_comfyui.bat
[2025-07-24 14:43:12.083] 
[2025-07-24 14:43:12.585] Context impl SQLiteImpl.
[2025-07-24 14:43:12.586] Will assume non-transactional DDL.
[2025-07-24 14:43:12.587] No target revision found.
[2025-07-24 14:43:12.612] Starting server

[2025-07-24 14:43:12.612] To see the GUI go to: http://127.0.0.1:8188
[2025-07-24 14:43:14.570] FETCH ComfyRegistry Data: 5/92
[2025-07-24 14:43:15.290] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\bizyengine\core\common\client.py:69: UserWarning: invalid api_key
  warnings.warn("invalid api_key")
[2025-07-24 14:43:20.841] FETCH ComfyRegistry Data: 10/92
[2025-07-24 14:43:25.565] FETCH ComfyRegistry Data: 15/92
[2025-07-24 14:43:30.896] FETCH ComfyRegistry Data: 20/92
[2025-07-24 14:43:35.915] FETCH ComfyRegistry Data: 25/92
[2025-07-24 14:43:40.846] got prompt
[2025-07-24 14:43:40.849] Failed to validate prompt for output 13:
[2025-07-24 14:43:40.849] * IPAdapter 7:
[2025-07-24 14:43:40.849]   - Required input is missing: image
[2025-07-24 14:43:40.849] * VAEEncode 10:
[2025-07-24 14:43:40.850]   - Required input is missing: pixels
[2025-07-24 14:43:40.850] Output will be ignored
[2025-07-24 14:43:40.850] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:43:40.908] FETCH ComfyRegistry Data: 30/92
[2025-07-24 14:43:46.007] FETCH ComfyRegistry Data: 35/92
[2025-07-24 14:43:51.115] FETCH ComfyRegistry Data: 40/92
[2025-07-24 14:43:56.943] FETCH ComfyRegistry Data: 45/92
[2025-07-24 14:43:57.663] got prompt
[2025-07-24 14:43:57.665] Failed to validate prompt for output 9:
[2025-07-24 14:43:57.665] * ControlNetLoader 35:
[2025-07-24 14:43:57.665]   - Value not in list: control_net_name: 'control_v11p_sd15_openpose_fp16.safetensors' not in ['control_v11f1p_sd15_depth.pth', 'control_v11p_sd15_canny.pth', 'control_v11p_sd15_openpose.pth']
[2025-07-24 14:43:57.665] * LoadImage 20:
[2025-07-24 14:43:57.665]   - Custom validation failed for node: image - Invalid image file: image (72).png
[2025-07-24 14:43:57.665] * ControlNetLoader 26:
[2025-07-24 14:43:57.665]   - Value not in list: control_net_name: 'control_v11p_sd15_scribble_fp16.safetensors' not in ['control_v11f1p_sd15_depth.pth', 'control_v11p_sd15_canny.pth', 'control_v11p_sd15_openpose.pth']
[2025-07-24 14:43:57.665] Output will be ignored
[2025-07-24 14:43:57.665] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:44:01.449] FETCH ComfyRegistry Data: 50/92
[2025-07-24 14:44:06.694] FETCH ComfyRegistry Data: 55/92
[2025-07-24 14:44:11.992] FETCH ComfyRegistry Data: 60/92
[2025-07-24 14:44:18.070] FETCH ComfyRegistry Data: 65/92
[2025-07-24 14:44:24.298] FETCH ComfyRegistry Data: 70/92
[2025-07-24 14:44:29.633] FETCH ComfyRegistry Data: 75/92
[2025-07-24 14:44:35.307] FETCH ComfyRegistry Data: 80/92
[2025-07-24 14:44:40.855] FETCH ComfyRegistry Data: 85/92
[2025-07-24 14:44:46.403] FETCH ComfyRegistry Data: 90/92
[2025-07-24 14:44:49.111] FETCH ComfyRegistry Data [DONE]
[2025-07-24 14:44:49.290] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-24 14:44:49.328] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-24 14:44:49.516] [ComfyUI-Manager] All startup tasks have been completed.
[2025-07-24 14:50:58.352] got prompt
[2025-07-24 14:50:58.353] Failed to validate prompt for output 13:
[2025-07-24 14:50:58.353] * IPAdapter 7:
[2025-07-24 14:50:58.353]   - Required input is missing: image
[2025-07-24 14:50:58.353] * VAEEncode 10:
[2025-07-24 14:50:58.353]   - Required input is missing: pixels
[2025-07-24 14:50:58.353] Output will be ignored
[2025-07-24 14:50:58.353] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:57:24.723] got prompt
[2025-07-24 14:57:24.725] Failed to validate prompt for output 8:
[2025-07-24 14:57:24.725] * KSampler 6:
[2025-07-24 14:57:24.725]   - Value not in list: sampler_name: 'euler_a' not in (list of length 40)
[2025-07-24 14:57:24.725] Output will be ignored
[2025-07-24 14:57:24.726] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:57:35.480] got prompt
[2025-07-24 14:57:35.788] model weight dtype torch.float32, manual cast: None
[2025-07-24 14:57:35.788] model_type EPS
[2025-07-24 14:57:39.531] Using split attention in VAE
[2025-07-24 14:57:39.545] Using split attention in VAE
[2025-07-24 14:57:39.878] VAE load device: cpu, offload device: cpu, dtype: torch.float32
[2025-07-24 14:57:40.327] Requested to load SD1ClipModel
[2025-07-24 14:57:40.335] loaded completely 9.5367431640625e+25 235.84423828125 True
[2025-07-24 14:57:40.338] CLIP/text encoder model load device: cpu, offload device: cpu, current: cpu, dtype: torch.float16
[2025-07-24 14:57:42.097] Requested to load AutoencoderKL
[2025-07-24 14:57:42.109] loaded completely 9.5367431640625e+25 319.11416244506836 True
[2025-07-24 14:57:49.938] Requested to load BaseModel
[2025-07-24 14:57:49.971] loaded completely 9.5367431640625e+25 3278.812271118164 True
[2025-07-24 14:59:46.464] 
100%|███████████████████████████████████████████████| 10/10 [01:56<00:00, 11.00s/it]
100%|███████████████████████████████████████████████| 10/10 [01:56<00:00, 11.65s/it]
[2025-07-24 14:59:57.291] Prompt executed in 141.80 seconds
[2025-07-24 15:04:08.907] Traceback (most recent call last):
[2025-07-24 15:04:08.907]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager\glob\manager_server.py", line 419, in do_install
    res = await core.unified_manager.install_by_id(node_name, version_spec, channel, mode, return_postinstall=skip_post_install)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-24 15:04:08.915]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager\glob\manager_core.py", line 1547, in install_by_id
    res = self.cnr_install(node_id, version_spec, instant_execution=instant_execution, no_deps=no_deps, return_postinstall=return_postinstall)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-24 15:04:08.917]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager\glob\manager_core.py", line 1315, in cnr_install
    manager_downloader.download_url(node_info.download_url, get_default_custom_nodes_path(), archive_name)
[2025-07-24 15:04:08.918]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager\glob\manager_downloader.py", line 58, in download_url
    return torchvision_download_url(model_url, model_dir, filename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-24 15:04:08.919]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchvision\datasets\utils.py", line 122, in download_url
    url = _get_redirect_url(url, max_hops=max_redirect_hops)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-24 15:04:08.919]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchvision\datasets\utils.py", line 66, in _get_redirect_url
    with urllib.request.urlopen(urllib.request.Request(url, headers=headers)) as response:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-24 15:04:08.919]   File "urllib\request.py", line 215, in urlopen
[2025-07-24 15:04:08.919]   File "urllib\request.py", line 521, in open
[2025-07-24 15:04:08.919]   File "urllib\request.py", line 630, in http_response
[2025-07-24 15:04:08.919]   File "urllib\request.py", line 559, in error
[2025-07-24 15:04:08.919]   File "urllib\request.py", line 492, in _call_chain
[2025-07-24 15:04:08.919]   File "urllib\request.py", line 639, in http_error_default
[2025-07-24 15:04:08.919] urllib.error.HTTPError: HTTP Error 404: Not Found
[2025-07-24 15:04:08.919] 
[ComfyUI-Manager] Queued works are completed.
{'install': 1}
[2025-07-24 15:04:08.919] 
After restarting ComfyUI, please refresh the browser.
