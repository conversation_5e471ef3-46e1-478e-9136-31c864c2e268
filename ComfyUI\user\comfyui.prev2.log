## ComfyUI-Manager: installing dependencies done.
[2025-07-24 14:30:51.461] ** ComfyUI startup time: 2025-07-24 14:30:51.461
[2025-07-24 14:30:51.461] ** Platform: Windows
[2025-07-24 14:30:51.461] ** Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-24 14:30:51.461] ** Python executable: C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\python.exe
[2025-07-24 14:30:51.466] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 14:30:51.466] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 14:30:51.466] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-24 14:30:51.466] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-24 14:30:51.466] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-24 14:30:54.472]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 14:30:54.472]    1.6 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 14:30:54.472]    5.7 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 14:30:54.472] 
[2025-07-24 14:30:56.983] Checkpoint files will always be loaded safely.
[2025-07-24 14:30:57.002] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-24 14:30:57.002] pytorch version: 2.7.1+cu128
[2025-07-24 14:30:57.013] Set vram state to: DISABLED
[2025-07-24 14:30:57.013] Device: cpu
[2025-07-24 14:31:14.539] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-24 14:31:49.229] Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-24 14:31:49.229] ComfyUI version: 0.3.45
[2025-07-24 14:31:49.297] ComfyUI frontend version: 1.23.4
[2025-07-24 14:31:49.299] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comfyui_frontend_package\static
[2025-07-24 14:31:59.231] [92m[BizyAir][0m BizyAir ComfyUI Plugin: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 14:31:59.655] Display name '☁️BizyAir CogView4_6B_Pipe' might differ from the native display name.
[2025-07-24 14:31:59.730] Display name '☁️BizyAir SetUnionControlNetType' might differ from the native display name.
[2025-07-24 14:31:59.731] Display name '☁️BizyAir ControlNetInpaintingAliMamaApply' might differ from the native display name.
[2025-07-24 14:31:59.747] Display name '☁️BizyAir DisableNoise' might differ from the native display name.
[2025-07-24 14:31:59.748] Display name '☁️BizyAir VPScheduler' might differ from the native display name.
[2025-07-24 14:31:59.748] Display name '☁️BizyAir SplitSigmas' might differ from the native display name.
[2025-07-24 14:31:59.750] Display name '☁️BizyAir SplitSigmasDenoise' might differ from the native display name.
[2025-07-24 14:31:59.750] Display name '☁️BizyAir FlipSigmas' might differ from the native display name.
[2025-07-24 14:31:59.751] Display name '☁️BizyAir CFGGuider' might differ from the native display name.
[2025-07-24 14:31:59.766] Display name '☁️BizyAir TrainDatasetAdd' might differ from the native display name.
[2025-07-24 14:31:59.793] Display name '☁️BizyAir CLIPTextEncodeFlux' might differ from the native display name.
[2025-07-24 14:31:59.793] Display name '☁️BizyAir ReferenceLatent' might differ from the native display name.
[2025-07-24 14:31:59.795] Display name '☁️BizyAir FluxGuidance' might differ from the native display name.
[2025-07-24 14:31:59.811] Display name '☁️BizyAir Hy3D_2_1SimpleMeshGen' might differ from the native display name.
[2025-07-24 14:31:59.812] Display name '☁️BizyAir Hy3DExportMesh' might differ from the native display name.
[2025-07-24 14:31:59.848] Display name '☁️BizyAir InstructPixToPixConditioning' might differ from the native display name.
[2025-07-24 14:31:59.883] Display name '☁️BizyAir IPAdapterModelLoader' might differ from the native display name.
[2025-07-24 14:31:59.884] Display name '☁️BizyAir IPAdapterAdvanced' might differ from the native display name.
[2025-07-24 14:31:59.884] Display name '☁️BizyAir IPAdapterStyleComposition' might differ from the native display name.
[2025-07-24 14:31:59.904] Display name '☁️BizyAir JanusModelLoader' might differ from the native display name.
[2025-07-24 14:31:59.905] Display name '☁️BizyAir JanusImageUnderstanding' might differ from the native display name.
[2025-07-24 14:31:59.905] Display name '☁️BizyAir JanusImageGeneration' might differ from the native display name.
[2025-07-24 14:31:59.943] Display name '☁️BizyAir ModelSamplingSD3' might differ from the native display name.
[2025-07-24 14:31:59.943] Display name '☁️BizyAir ModelSamplingFlux' might differ from the native display name.
[2025-07-24 14:31:59.957] Display name '☁️BizyAir NunchakuPulidApply' might differ from the native display name.
[2025-07-24 14:31:59.958] Display name '☁️BizyAir NunchakuPulidLoader' might differ from the native display name.
[2025-07-24 14:32:00.006] Display name '☁️BizyAir TripleCLIPLoader' might differ from the native display name.
[2025-07-24 14:32:00.083] Display name '☁️BizyAir UltimateSDUpscale' might differ from the native display name.
[2025-07-24 14:32:00.130] Display name '☁️BizyAir Wan_LoraLoader' might differ from the native display name.
[2025-07-24 14:32:00.131] Display name '☁️BizyAir Wan_ImageToVideoPipeline' might differ from the native display name.
[2025-07-24 14:32:00.145] Display name '☁️BizyAir Wan_Model_Loader' might differ from the native display name.
[2025-07-24 14:32:00.146] Display name '☁️BizyAir Wan_T2V_Pipeline' might differ from the native display name.
[2025-07-24 14:32:00.240] Display name '☁️BizyAir LoraLoader_Legacy' might differ from the native display name.
[2025-07-24 14:32:00.240] Display name '☁️BizyAir ControlNetLoader_Legacy' might differ from the native display name.
[2025-07-24 14:32:00.242] Display name '☁️BizyAir SamplerCustomAdvanced' might differ from the native display name.
[2025-07-24 14:32:00.242] Display name '☁️BizyAir BasicGuider' might differ from the native display name.
[2025-07-24 14:32:00.244] Display name '☁️BizyAir BasicScheduler' might differ from the native display name.
[2025-07-24 14:32:00.244] Display name '☁️BizyAir DualCLIPLoader' might differ from the native display name.
[2025-07-24 14:32:00.245] Display name '☁️BizyAir KSamplerSelect' might differ from the native display name.
[2025-07-24 14:32:00.245] Display name '☁️BizyAir RandomNoise' might differ from the native display name.
[2025-07-24 14:32:00.246] Display name '☁️BizyAir InpaintModelConditioning' might differ from the native display name.
[2025-07-24 14:32:00.247] Display name '☁️BizyAir InpaintModelConditioning_v2' might differ from the native display name.
[2025-07-24 14:32:00.248] Display name '☁️BizyAir ConditioningZeroOut' might differ from the native display name.
[2025-07-24 14:32:00.248] Display name '☁️BizyAir ConditioningSetTimestepRange' might differ from the native display name.
[2025-07-24 14:32:00.249] Display name '☁️BizyAir PassParameter' might differ from the native display name.
[2025-07-24 14:32:07.383] 

[92m[BizyAir][0m Model hosting service initialized.

[2025-07-24 14:32:07.384] 
[2025-07-24 14:32:17.576] Package diffusers installed failed
[2025-07-24 14:32:19.889] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\__init__.py", line 15, in <module>
    importlib.import_module('.py.routes', __name__)
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\py\routes.py", line 13, in <module>
    from .libs.translate import has_chinese, zh_to_en
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\py\libs\translate.py", line 9, in <module>
    from transformers import AutoModelForSeq2SeqLM, AutoTokenizer
  File "<frozen importlib._bootstrap>", line 1229, in _handle_fromlist
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2154, in __getattr__
    module = self._get_module(self._class_to_module[name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2184, in _get_module
    raise e
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2182, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\models\auto\modeling_auto.py", line 21, in <module>
    from .auto_factory import (
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\models\auto\auto_factory.py", line 43, in <module>
    from ...generation import GenerationMixin
  File "<frozen importlib._bootstrap>", line 1229, in _handle_fromlist
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2154, in __getattr__
    module = self._get_module(self._class_to_module[name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2184, in _get_module
    raise e
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2182, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\generation\utils.py", line 63, in <module>
    from .candidate_generator import (
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\generation\candidate_generator.py", line 29, in <module>
    from sklearn.metrics import roc_curve
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\sklearn\__init__.py", line 73, in <module>
    from .base import clone  # noqa: E402
    ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\sklearn\base.py", line 19, in <module>
    from .utils._metadata_requests import _MetadataRequester, _routing_enabled
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\sklearn\utils\__init__.py", line 9, in <module>
    from ._chunking import gen_batches, gen_even_slices
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\sklearn\utils\_chunking.py", line 11, in <module>
    from ._param_validation import Interval, validate_params
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\sklearn\utils\_param_validation.py", line 17, in <module>
    from .validation import _is_arraylike_not_scalar
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\sklearn\utils\validation.py", line 21, in <module>
    from ..utils._array_api import _asarray_with_order, _is_numpy_namespace, get_namespace
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\sklearn\utils\_array_api.py", line 20, in <module>
    from .fixes import parse_version
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\sklearn\utils\fixes.py", line 20, in <module>
    import pandas as pd
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\pandas\__init__.py", line 59, in <module>
    from pandas.core.api import (
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\pandas\core\api.py", line 1, in <module>
    from pandas._libs import (
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\pandas\_libs\__init__.py", line 18, in <module>
    from pandas._libs.interval import Interval
  File "interval.pyx", line 1, in init pandas._libs.interval
ValueError: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject

[2025-07-24 14:32:19.889] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use module for custom nodes: numpy.dtype size changed, may indicate binary incompatibility. Expected 96 from C header, got 88 from PyObject
[2025-07-24 14:32:20.286] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade\__init__.py", line 1, in <module>
    from .nodes.jake_upgrade import *
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade\nodes\jake_upgrade.py", line 37, in <module>
    import piexif
ModuleNotFoundError: No module named 'piexif'

[2025-07-24 14:32:20.286] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade module for custom nodes: No module named 'piexif'
[2025-07-24 14:32:20.351] ### Loading: ComfyUI-Manager (V3.34.1)
[2025-07-24 14:32:20.352] [ComfyUI-Manager] network_mode: public
[2025-07-24 14:32:20.536] ### ComfyUI Revision: 150 [9a470e07] *DETACHED | Released on '2025-07-21'
[2025-07-24 14:32:20.605] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-24 14:32:20.606] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-24 14:32:20.608] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-24 14:32:20.660] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-24 14:32:20.683] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-24 14:32:20.735] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-24 14:32:20.799] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-24 14:32:20.971] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-24 14:32:21.277] 
A module that was compiled using NumPy 1.x cannot be run in
NumPy 2.1.3 as it may crash. To support both 1.x and 2.x
versions of NumPy, modules must be compiled with NumPy 2.0.
Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.

If you are a user of the module, the easiest solution will be to
downgrade to 'numpy<2' or try to upgrade the affected module.
We expect that some modules will need time to support NumPy 2.

Traceback (most recent call last):  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 352, in <module>
    event_loop, _, start_all_func = start_comfyui()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 307, in start_comfyui
    nodes.init_extra_nodes(
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2337, in init_extra_nodes
    init_external_custom_nodes()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2194, in init_external_custom_nodes
    success = load_custom_node(module_path, base_node_names, module_parent="custom_nodes")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\__init__.py", line 66, in <module>
    AUX_NODE_MAPPINGS, AUX_DISPLAY_NAME_MAPPINGS = load_nodes()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\__init__.py", line 35, in load_nodes
    module = importlib.import_module(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py", line 5, in <module>
    from custom_controlnet_aux.dwpose import DwposeDetector, AnimalposeDetector
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\src\custom_controlnet_aux\dwpose\__init__.py", line 15, in <module>
    from . import util
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\src\custom_controlnet_aux\dwpose\util.py", line 3, in <module>
    import matplotlib
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\__init__.py", line 148, in <module>
    from . import _api, _version, cbook, _docstring, rcsetup
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\rcsetup.py", line 27, in <module>
    from matplotlib.colors import Colormap, is_color_like
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\colors.py", line 56, in <module>
    from matplotlib import _api, _cm, cbook, scale
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\scale.py", line 22, in <module>
    from matplotlib.ticker import (
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\ticker.py", line 138, in <module>
    from matplotlib import transforms as mtransforms
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\transforms.py", line 49, in <module>
    from matplotlib._path import (
[2025-07-24 14:32:21.280] AttributeError: _ARRAY_API not found
[2025-07-24 14:32:21.391] 
A module that was compiled using NumPy 1.x cannot be run in
NumPy 2.1.3 as it may crash. To support both 1.x and 2.x
versions of NumPy, modules must be compiled with NumPy 2.0.
Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.

If you are a user of the module, the easiest solution will be to
downgrade to 'numpy<2' or try to upgrade the affected module.
We expect that some modules will need time to support NumPy 2.

Traceback (most recent call last):  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 352, in <module>
    event_loop, _, start_all_func = start_comfyui()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 307, in start_comfyui
    nodes.init_extra_nodes(
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2337, in init_extra_nodes
    init_external_custom_nodes()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2194, in init_external_custom_nodes
    success = load_custom_node(module_path, base_node_names, module_parent="custom_nodes")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\__init__.py", line 66, in <module>
    AUX_NODE_MAPPINGS, AUX_DISPLAY_NAME_MAPPINGS = load_nodes()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\__init__.py", line 35, in load_nodes
    module = importlib.import_module(
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\pose_keypoint_postprocess.py", line 11, in <module>
    from ..src.custom_controlnet_aux.dwpose import draw_poses, draw_animalposes, decode_json_as_poses
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\src\custom_controlnet_aux\dwpose\__init__.py", line 15, in <module>
    from . import util
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\src\custom_controlnet_aux\dwpose\util.py", line 3, in <module>
    import matplotlib
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\__init__.py", line 148, in <module>
    from . import _api, _version, cbook, _docstring, rcsetup
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\rcsetup.py", line 27, in <module>
    from matplotlib.colors import Colormap, is_color_like
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\colors.py", line 56, in <module>
    from matplotlib import _api, _cm, cbook, scale
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\scale.py", line 22, in <module>
    from matplotlib.ticker import (
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\ticker.py", line 138, in <module>
    from matplotlib import transforms as mtransforms
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\transforms.py", line 49, in <module>
    from matplotlib._path import (
[2025-07-24 14:32:21.391] AttributeError: _ARRAY_API not found
[2025-07-24 14:32:21.449] 

Full error log from comfyui_controlnet_aux: 
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\__init__.py", line 35, in load_nodes
    module = importlib.import_module(
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py", line 5, in <module>
    from custom_controlnet_aux.dwpose import DwposeDetector, AnimalposeDetector
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\src\custom_controlnet_aux\dwpose\__init__.py", line 15, in <module>
    from . import util
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\src\custom_controlnet_aux\dwpose\util.py", line 3, in <module>
    import matplotlib
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\__init__.py", line 148, in <module>
    from . import _api, _version, cbook, _docstring, rcsetup
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\rcsetup.py", line 27, in <module>
    from matplotlib.colors import Colormap, is_color_like
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\colors.py", line 56, in <module>
    from matplotlib import _api, _cm, cbook, scale
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\scale.py", line 22, in <module>
    from matplotlib.ticker import (
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\ticker.py", line 138, in <module>
    from matplotlib import transforms as mtransforms
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\transforms.py", line 49, in <module>
    from matplotlib._path import (
ImportError: numpy.core.multiarray failed to import


Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\__init__.py", line 35, in load_nodes
    module = importlib.import_module(
             ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\pose_keypoint_postprocess.py", line 11, in <module>
    from ..src.custom_controlnet_aux.dwpose import draw_poses, draw_animalposes, decode_json_as_poses
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\src\custom_controlnet_aux\dwpose\__init__.py", line 15, in <module>
    from . import util
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\src\custom_controlnet_aux\dwpose\util.py", line 3, in <module>
    import matplotlib
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\__init__.py", line 148, in <module>
    from . import _api, _version, cbook, _docstring, rcsetup
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\rcsetup.py", line 27, in <module>
    from matplotlib.colors import Colormap, is_color_like
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\colors.py", line 56, in <module>
    from matplotlib import _api, _cm, cbook, scale
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\scale.py", line 22, in <module>
    from matplotlib.ticker import (
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\ticker.py", line 138, in <module>
    from matplotlib import transforms as mtransforms
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\matplotlib\transforms.py", line 49, in <module>
    from matplotlib._path import (
ImportError: numpy.core.multiarray failed to import


[2025-07-24 14:32:21.450] 
[2025-07-24 14:32:21.451] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Some nodes failed to load:
	Failed to import module dwpose because ImportError: numpy.core.multiarray failed to import
	Failed to import module pose_keypoint_postprocess because ImportError: numpy.core.multiarray failed to import

Check that you properly installed the dependencies.
If you think this is a bug, please report it on the github page (https://github.com/Fannovel16/comfyui_controlnet_aux/issues)[0m
[2025-07-24 14:32:21.485] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\__init__.py", line 1, in <module>
    from .faceanalysis import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\faceanalysis.py", line 16, in <module>
    raise Exception("Please install either dlib or insightface to use this node.")
Exception: Please install either dlib or insightface to use this node.

[2025-07-24 14:32:21.487] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis module for custom nodes: Please install either dlib or insightface to use this node.
[2025-07-24 14:32:26.195] FETCH ComfyRegistry Data: 5/92
[2025-07-24 14:32:28.305] 
A module that was compiled using NumPy 1.x cannot be run in
NumPy 2.1.3 as it may crash. To support both 1.x and 2.x
versions of NumPy, modules must be compiled with NumPy 2.0.
Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.

If you are a user of the module, the easiest solution will be to
downgrade to 'numpy<2' or try to upgrade the affected module.
We expect that some modules will need time to support NumPy 2.

Traceback (most recent call last):  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 352, in <module>
    event_loop, _, start_all_func = start_comfyui()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 307, in start_comfyui
    nodes.init_extra_nodes(
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2337, in init_extra_nodes
    init_external_custom_nodes()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2194, in init_external_custom_nodes
    success = load_custom_node(module_path, base_node_names, module_parent="custom_nodes")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\__init__.py", line 1, in <module>
    from .WAS_Node_Suite import NODE_CLASS_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\WAS_Node_Suite.py", line 2459, in <module>
    from transformers import BlipProcessor, BlipForConditionalGeneration, BlipForQuestionAnswering
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2154, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2182, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\models\blip\processing_blip.py", line 22, in <module>
    from ...processing_utils import ProcessingKwargs, ProcessorMixin, Unpack
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\processing_utils.py", line 38, in <module>
    from .video_utils import VideoMetadata, load_video
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\video_utils.py", line 28, in <module>
    from .image_transforms import PaddingMode, to_channel_dimension_format
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\image_transforms.py", line 48, in <module>
    import tensorflow as tf
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\__init__.py", line 38, in <module>
    from tensorflow.python.tools import module_util as _module_util
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\__init__.py", line 37, in <module>
    from tensorflow.python.eager import context
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\eager\context.py", line 34, in <module>
    from tensorflow.python.client import pywrap_tf_session
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\client\pywrap_tf_session.py", line 19, in <module>
    from tensorflow.python.client._pywrap_tf_session import *
[2025-07-24 14:32:28.308] AttributeError: _ARRAY_API not found
[2025-07-24 14:32:28.780] 
A module that was compiled using NumPy 1.x cannot be run in
NumPy 2.1.3 as it may crash. To support both 1.x and 2.x
versions of NumPy, modules must be compiled with NumPy 2.0.
Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.

If you are a user of the module, the easiest solution will be to
downgrade to 'numpy<2' or try to upgrade the affected module.
We expect that some modules will need time to support NumPy 2.

Traceback (most recent call last):  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 352, in <module>
    event_loop, _, start_all_func = start_comfyui()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 307, in start_comfyui
    nodes.init_extra_nodes(
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2337, in init_extra_nodes
    init_external_custom_nodes()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2194, in init_external_custom_nodes
    success = load_custom_node(module_path, base_node_names, module_parent="custom_nodes")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\__init__.py", line 1, in <module>
    from .WAS_Node_Suite import NODE_CLASS_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\WAS_Node_Suite.py", line 2459, in <module>
    from transformers import BlipProcessor, BlipForConditionalGeneration, BlipForQuestionAnswering
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2154, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2182, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\models\blip\processing_blip.py", line 22, in <module>
    from ...processing_utils import ProcessingKwargs, ProcessorMixin, Unpack
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\processing_utils.py", line 38, in <module>
    from .video_utils import VideoMetadata, load_video
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\video_utils.py", line 28, in <module>
    from .image_transforms import PaddingMode, to_channel_dimension_format
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\image_transforms.py", line 48, in <module>
    import tensorflow as tf
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\__init__.py", line 38, in <module>
    from tensorflow.python.tools import module_util as _module_util
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\__init__.py", line 37, in <module>
    from tensorflow.python.eager import context
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\eager\context.py", line 36, in <module>
    from tensorflow.python.eager import execute
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\eager\execute.py", line 21, in <module>
    from tensorflow.python.framework import dtypes
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\framework\dtypes.py", line 30, in <module>
    from tensorflow.python.lib.core import _pywrap_float8
[2025-07-24 14:32:28.784] AttributeError: _ARRAY_API not found
[2025-07-24 14:32:28.790] ImportError: numpy.core._multiarray_umath failed to import
[2025-07-24 14:32:28.799] ImportError: numpy.core.umath failed to import
[2025-07-24 14:32:28.809] 
A module that was compiled using NumPy 1.x cannot be run in
NumPy 2.1.3 as it may crash. To support both 1.x and 2.x
versions of NumPy, modules must be compiled with NumPy 2.0.
Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.

If you are a user of the module, the easiest solution will be to
downgrade to 'numpy<2' or try to upgrade the affected module.
We expect that some modules will need time to support NumPy 2.

Traceback (most recent call last):  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 352, in <module>
    event_loop, _, start_all_func = start_comfyui()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 307, in start_comfyui
    nodes.init_extra_nodes(
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2337, in init_extra_nodes
    init_external_custom_nodes()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2194, in init_external_custom_nodes
    success = load_custom_node(module_path, base_node_names, module_parent="custom_nodes")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\__init__.py", line 1, in <module>
    from .WAS_Node_Suite import NODE_CLASS_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\WAS_Node_Suite.py", line 2459, in <module>
    from transformers import BlipProcessor, BlipForConditionalGeneration, BlipForQuestionAnswering
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2154, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2182, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\models\blip\processing_blip.py", line 22, in <module>
    from ...processing_utils import ProcessingKwargs, ProcessorMixin, Unpack
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\processing_utils.py", line 38, in <module>
    from .video_utils import VideoMetadata, load_video
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\video_utils.py", line 28, in <module>
    from .image_transforms import PaddingMode, to_channel_dimension_format
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\image_transforms.py", line 48, in <module>
    import tensorflow as tf
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\__init__.py", line 38, in <module>
    from tensorflow.python.tools import module_util as _module_util
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\__init__.py", line 37, in <module>
    from tensorflow.python.eager import context
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\eager\context.py", line 36, in <module>
    from tensorflow.python.eager import execute
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\eager\execute.py", line 21, in <module>
    from tensorflow.python.framework import dtypes
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\framework\dtypes.py", line 30, in <module>
    from tensorflow.python.lib.core import _pywrap_float8
[2025-07-24 14:32:28.810] AttributeError: _ARRAY_API not found
[2025-07-24 14:32:28.818] ImportError: numpy.core._multiarray_umath failed to import
[2025-07-24 14:32:28.825] ImportError: numpy.core.umath failed to import
[2025-07-24 14:32:29.007] 
A module that was compiled using NumPy 1.x cannot be run in
NumPy 2.1.3 as it may crash. To support both 1.x and 2.x
versions of NumPy, modules must be compiled with NumPy 2.0.
Some module may need to rebuild instead e.g. with 'pybind11>=2.12'.

If you are a user of the module, the easiest solution will be to
downgrade to 'numpy<2' or try to upgrade the affected module.
We expect that some modules will need time to support NumPy 2.

Traceback (most recent call last):  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 352, in <module>
    event_loop, _, start_all_func = start_comfyui()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 307, in start_comfyui
    nodes.init_extra_nodes(
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2337, in init_extra_nodes
    init_external_custom_nodes()
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2194, in init_external_custom_nodes
    success = load_custom_node(module_path, base_node_names, module_parent="custom_nodes")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\__init__.py", line 1, in <module>
    from .WAS_Node_Suite import NODE_CLASS_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\WAS_Node_Suite.py", line 2459, in <module>
    from transformers import BlipProcessor, BlipForConditionalGeneration, BlipForQuestionAnswering
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2154, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2182, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\models\blip\processing_blip.py", line 22, in <module>
    from ...processing_utils import ProcessingKwargs, ProcessorMixin, Unpack
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\processing_utils.py", line 38, in <module>
    from .video_utils import VideoMetadata, load_video
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\video_utils.py", line 28, in <module>
    from .image_transforms import PaddingMode, to_channel_dimension_format
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\image_transforms.py", line 48, in <module>
    import tensorflow as tf
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\__init__.py", line 38, in <module>
    from tensorflow.python.tools import module_util as _module_util
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\__init__.py", line 37, in <module>
    from tensorflow.python.eager import context
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\eager\context.py", line 36, in <module>
    from tensorflow.python.eager import execute
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\eager\execute.py", line 21, in <module>
    from tensorflow.python.framework import dtypes
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\framework\dtypes.py", line 35, in <module>
    from tensorflow.tsl.python.lib.core import pywrap_bfloat16
[2025-07-24 14:32:29.010] AttributeError: _ARRAY_API not found
[2025-07-24 14:32:29.019] ImportError: numpy.core._multiarray_umath failed to import
[2025-07-24 14:32:29.027] ImportError: numpy.core.umath failed to import
[2025-07-24 14:32:29.048] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\__init__.py", line 1, in <module>
    from .WAS_Node_Suite import NODE_CLASS_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\WAS_Node_Suite.py", line 2459, in <module>
    from transformers import BlipProcessor, BlipForConditionalGeneration, BlipForQuestionAnswering
  File "<frozen importlib._bootstrap>", line 1229, in _handle_fromlist
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2154, in __getattr__
    module = self._get_module(self._class_to_module[name])
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2184, in _get_module
    raise e
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\utils\import_utils.py", line 2182, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.11_3.11.2544.0_x64__qbz5n2kfra8p0\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\models\blip\processing_blip.py", line 22, in <module>
    from ...processing_utils import ProcessingKwargs, ProcessorMixin, Unpack
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\processing_utils.py", line 38, in <module>
    from .video_utils import VideoMetadata, load_video
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\video_utils.py", line 28, in <module>
    from .image_transforms import PaddingMode, to_channel_dimension_format
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\transformers\image_transforms.py", line 48, in <module>
    import tensorflow as tf
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\__init__.py", line 38, in <module>
    from tensorflow.python.tools import module_util as _module_util
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\__init__.py", line 37, in <module>
    from tensorflow.python.eager import context
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\eager\context.py", line 36, in <module>
    from tensorflow.python.eager import execute
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\eager\execute.py", line 21, in <module>
    from tensorflow.python.framework import dtypes
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\tensorflow\python\framework\dtypes.py", line 38, in <module>
    _np_bfloat16 = pywrap_bfloat16.bfloat16_type()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: Unable to convert function return value to a Python type! The signature was
	() -> handle

[2025-07-24 14:32:29.048] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns module for custom nodes: Unable to convert function return value to a Python type! The signature was
	() -> handle
[2025-07-24 14:32:29.054] 
Import times for custom nodes:
[2025-07-24 14:32:29.054]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-24 14:32:29.055]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis
[2025-07-24 14:32:29.055]    0.3 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 14:32:29.055]    0.4 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade
[2025-07-24 14:32:29.055]    0.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-24 14:32:29.055]    0.9 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-24 14:32:29.056]    7.1 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns
[2025-07-24 14:32:29.056]    8.3 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 14:32:29.057]   12.5 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 14:32:29.057] 
[2025-07-24 14:32:30.973] FETCH ComfyRegistry Data: 10/92
[2025-07-24 14:32:32.038] Context impl SQLiteImpl.
[2025-07-24 14:32:32.038] Will assume non-transactional DDL.
[2025-07-24 14:32:32.042] No target revision found.
[2025-07-24 14:32:32.094] Starting server

[2025-07-24 14:32:32.097] To see the GUI go to: http://127.0.0.1:8188
[2025-07-24 14:32:35.486] C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\bizyengine\core\common\client.py:69: UserWarning: invalid api_key
  warnings.warn("invalid api_key")
[2025-07-24 14:32:35.938] FETCH ComfyRegistry Data: 15/92
[2025-07-24 14:32:39.317] got prompt
[2025-07-24 14:32:39.319] Failed to validate prompt for output 13:
[2025-07-24 14:32:39.320] * IPAdapter 7:
[2025-07-24 14:32:39.320]   - Required input is missing: image
[2025-07-24 14:32:39.321] * VAEEncode 10:
[2025-07-24 14:32:39.321]   - Required input is missing: pixels
[2025-07-24 14:32:39.321] Output will be ignored
[2025-07-24 14:32:39.321] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:32:41.711] FETCH ComfyRegistry Data: 20/92
[2025-07-24 14:32:46.693] FETCH ComfyRegistry Data: 25/92
[2025-07-24 14:32:51.817] FETCH ComfyRegistry Data: 30/92
[2025-07-24 14:32:53.099] got prompt
[2025-07-24 14:32:53.103] Failed to validate prompt for output 13:
[2025-07-24 14:32:53.103] * IPAdapter 7:
[2025-07-24 14:32:53.103]   - Required input is missing: image
[2025-07-24 14:32:53.103] * VAEEncode 10:
[2025-07-24 14:32:53.103]   - Required input is missing: pixels
[2025-07-24 14:32:53.103] Output will be ignored
[2025-07-24 14:32:53.103] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:32:56.899] FETCH ComfyRegistry Data: 35/92
[2025-07-24 14:33:01.961] FETCH ComfyRegistry Data: 40/92
[2025-07-24 14:33:03.915] got prompt
[2025-07-24 14:33:03.916] Failed to validate prompt for output 13:
[2025-07-24 14:33:03.916] * IPAdapter 7:
[2025-07-24 14:33:03.916]   - Required input is missing: image
[2025-07-24 14:33:03.916] * VAEEncode 10:
[2025-07-24 14:33:03.916]   - Required input is missing: pixels
[2025-07-24 14:33:03.921] Output will be ignored
[2025-07-24 14:33:03.921] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:33:06.909] FETCH ComfyRegistry Data: 45/92
[2025-07-24 14:33:13.212] FETCH ComfyRegistry Data: 50/92
[2025-07-24 14:33:19.376] FETCH ComfyRegistry Data: 55/92
[2025-07-24 14:33:25.552] FETCH ComfyRegistry Data: 60/92
[2025-07-24 14:33:32.151] FETCH ComfyRegistry Data: 65/92
[2025-07-24 14:33:37.630] FETCH ComfyRegistry Data: 70/92
[2025-07-24 14:33:43.707] FETCH ComfyRegistry Data: 75/92
[2025-07-24 14:33:48.666] FETCH ComfyRegistry Data: 80/92
[2025-07-24 14:33:55.918] FETCH ComfyRegistry Data: 85/92
[2025-07-24 14:34:01.124] FETCH ComfyRegistry Data: 90/92
[2025-07-24 14:34:03.795] FETCH ComfyRegistry Data [DONE]
[2025-07-24 14:34:03.999] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-24 14:34:04.031] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-24 14:34:04.451] [ComfyUI-Manager] All startup tasks have been completed.
[2025-07-24 14:34:41.564] got prompt
[2025-07-24 14:34:41.566] Failed to validate prompt for output 9:
[2025-07-24 14:34:41.566] * ControlNetLoader 35:
[2025-07-24 14:34:41.566]   - Value not in list: control_net_name: 'control_v11p_sd15_openpose_fp16.safetensors' not in ['control_v11f1p_sd15_depth.pth', 'control_v11p_sd15_canny.pth', 'control_v11p_sd15_openpose.pth']
[2025-07-24 14:34:41.566] * LoadImage 20:
[2025-07-24 14:34:41.566]   - Custom validation failed for node: image - Invalid image file: image (72).png
[2025-07-24 14:34:41.566] * ControlNetLoader 26:
[2025-07-24 14:34:41.566]   - Value not in list: control_net_name: 'control_v11p_sd15_scribble_fp16.safetensors' not in ['control_v11f1p_sd15_depth.pth', 'control_v11p_sd15_canny.pth', 'control_v11p_sd15_openpose.pth']
[2025-07-24 14:34:41.566] Output will be ignored
[2025-07-24 14:34:41.566] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:34:51.846] got prompt
[2025-07-24 14:34:51.852] Failed to validate prompt for output 9:
[2025-07-24 14:34:51.852] * ControlNetLoader 35:
[2025-07-24 14:34:51.852]   - Value not in list: control_net_name: 'control_v11p_sd15_openpose_fp16.safetensors' not in ['control_v11f1p_sd15_depth.pth', 'control_v11p_sd15_canny.pth', 'control_v11p_sd15_openpose.pth']
[2025-07-24 14:34:51.852] * LoadImage 20:
[2025-07-24 14:34:51.852]   - Custom validation failed for node: image - Invalid image file: image (72).png
[2025-07-24 14:34:51.852] * ControlNetLoader 26:
[2025-07-24 14:34:51.852]   - Value not in list: control_net_name: 'control_v11p_sd15_scribble_fp16.safetensors' not in ['control_v11f1p_sd15_depth.pth', 'control_v11p_sd15_canny.pth', 'control_v11p_sd15_openpose.pth']
[2025-07-24 14:34:51.852] Output will be ignored
[2025-07-24 14:34:51.852] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:35:37.787] got prompt
[2025-07-24 14:35:37.794] Failed to validate prompt for output 9:
[2025-07-24 14:35:37.794] * ControlNetLoader 35:
[2025-07-24 14:35:37.795]   - Value not in list: control_net_name: 'control_v11p_sd15_openpose_fp16.safetensors' not in ['control_v11f1p_sd15_depth.pth', 'control_v11p_sd15_canny.pth', 'control_v11p_sd15_openpose.pth']
[2025-07-24 14:35:37.795] * LoadImage 20:
[2025-07-24 14:35:37.795]   - Custom validation failed for node: image - Invalid image file: image (72).png
[2025-07-24 14:35:37.795] * ControlNetLoader 26:
[2025-07-24 14:35:37.797]   - Value not in list: control_net_name: 'control_v11p_sd15_scribble_fp16.safetensors' not in ['control_v11f1p_sd15_depth.pth', 'control_v11p_sd15_canny.pth', 'control_v11p_sd15_openpose.pth']
[2025-07-24 14:35:37.797] Output will be ignored
[2025-07-24 14:35:37.797] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:37:07.656] got prompt
[2025-07-24 14:37:07.661] Failed to validate prompt for output 13:
[2025-07-24 14:37:07.661] * IPAdapter 7:
[2025-07-24 14:37:07.661]   - Required input is missing: image
[2025-07-24 14:37:07.661] * VAEEncode 10:
[2025-07-24 14:37:07.665]   - Required input is missing: pixels
[2025-07-24 14:37:07.666] Output will be ignored
[2025-07-24 14:37:07.666] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:37:15.995] got prompt
[2025-07-24 14:37:15.998] Failed to validate prompt for output 13:
[2025-07-24 14:37:15.998] * IPAdapter 7:
[2025-07-24 14:37:15.998]   - Required input is missing: image
[2025-07-24 14:37:15.998] * VAEEncode 10:
[2025-07-24 14:37:15.998]   - Required input is missing: pixels
[2025-07-24 14:37:15.998] Output will be ignored
[2025-07-24 14:37:15.998] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:37:28.371] got prompt
[2025-07-24 14:37:28.374] Failed to validate prompt for output 13:
[2025-07-24 14:37:28.374] * IPAdapter 7:
[2025-07-24 14:37:28.374]   - Required input is missing: image
[2025-07-24 14:37:28.374] * VAEEncode 10:
[2025-07-24 14:37:28.375]   - Required input is missing: pixels
[2025-07-24 14:37:28.375] Output will be ignored
[2025-07-24 14:37:28.375] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 14:37:48.361] got prompt
[2025-07-24 14:37:48.364] Failed to validate prompt for output 13:
[2025-07-24 14:37:48.364] * IPAdapter 7:
[2025-07-24 14:37:48.364]   - Required input is missing: image
[2025-07-24 14:37:48.366] * VAEEncode 10:
[2025-07-24 14:37:48.366]   - Required input is missing: pixels
[2025-07-24 14:37:48.366] Output will be ignored
[2025-07-24 14:37:48.367] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
