## ComfyUI-Manager: installing dependencies done.
[2025-07-24 10:51:09.733] ** ComfyUI startup time: 2025-07-24 10:51:09.733
[2025-07-24 10:51:09.734] ** Platform: Windows
[2025-07-24 10:51:09.734] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 10:51:09.736] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-24 10:51:09.737] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 10:51:09.737] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 10:51:09.738] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-24 10:51:09.738] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-24 10:51:09.739] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-24 10:51:11.403]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 10:51:11.403]    1.4 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 10:51:11.403]    3.4 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 10:51:11.403] 
[2025-07-24 10:51:14.775] Checkpoint files will always be loaded safely.
[2025-07-24 10:51:14.805] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-24 10:51:14.805] pytorch version: 2.4.1+cpu
[2025-07-24 10:51:14.805] Set vram state to: DISABLED
[2025-07-24 10:51:14.805] Device: cpu
[2025-07-24 10:51:17.607] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-24 10:51:17.660] torchaudio missing, ACE model will be broken
[2025-07-24 10:51:17.670] torchaudio missing, ACE model will be broken
[2025-07-24 10:51:28.478] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 10:51:28.478] ComfyUI version: 0.3.45
[2025-07-24 10:51:28.509] ComfyUI frontend version: 1.23.4
[2025-07-24 10:51:28.511] [Prompt Server] web root: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-07-24 10:51:29.837] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py", line 4, in <module>
    import torchaudio
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\__init__.py", line 2, in <module>
    from . import _extension  # noqa  # usort: skip
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\__init__.py", line 38, in <module>
    _load_lib("libtorchaudio")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\utils.py", line 60, in _load_lib
    torch.ops.load_library(path)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\_ops.py", line 1295, in load_library
    ctypes.CDLL(path)
  File "ctypes\__init__.py", line 379, in __init__
FileNotFoundError: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.

[2025-07-24 10:51:29.838] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py module for custom nodes: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
[2025-07-24 10:51:30.415] [92m[BizyAir][0m BizyAir ComfyUI Plugin: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 10:51:30.500] Display name '☁️BizyAir CogView4_6B_Pipe' might differ from the native display name.
[2025-07-24 10:51:30.507] Display name '☁️BizyAir SetUnionControlNetType' might differ from the native display name.
[2025-07-24 10:51:30.508] Display name '☁️BizyAir ControlNetInpaintingAliMamaApply' might differ from the native display name.
[2025-07-24 10:51:30.509] Display name '☁️BizyAir DisableNoise' might differ from the native display name.
[2025-07-24 10:51:30.510] Display name '☁️BizyAir VPScheduler' might differ from the native display name.
[2025-07-24 10:51:30.511] Display name '☁️BizyAir SplitSigmas' might differ from the native display name.
[2025-07-24 10:51:30.512] Display name '☁️BizyAir SplitSigmasDenoise' might differ from the native display name.
[2025-07-24 10:51:30.512] Display name '☁️BizyAir FlipSigmas' might differ from the native display name.
[2025-07-24 10:51:30.513] Display name '☁️BizyAir CFGGuider' might differ from the native display name.
[2025-07-24 10:51:30.516] Display name '☁️BizyAir TrainDatasetAdd' might differ from the native display name.
[2025-07-24 10:51:30.518] Display name '☁️BizyAir CLIPTextEncodeFlux' might differ from the native display name.
[2025-07-24 10:51:30.519] Display name '☁️BizyAir ReferenceLatent' might differ from the native display name.
[2025-07-24 10:51:30.520] Display name '☁️BizyAir FluxGuidance' might differ from the native display name.
[2025-07-24 10:51:30.521] Display name '☁️BizyAir Hy3D_2_1SimpleMeshGen' might differ from the native display name.
[2025-07-24 10:51:30.521] Display name '☁️BizyAir Hy3DExportMesh' might differ from the native display name.
[2025-07-24 10:51:30.524] Display name '☁️BizyAir InstructPixToPixConditioning' might differ from the native display name.
[2025-07-24 10:51:30.528] Display name '☁️BizyAir IPAdapterModelLoader' might differ from the native display name.
[2025-07-24 10:51:30.528] Display name '☁️BizyAir IPAdapterAdvanced' might differ from the native display name.
[2025-07-24 10:51:30.529] Display name '☁️BizyAir IPAdapterStyleComposition' might differ from the native display name.
[2025-07-24 10:51:30.530] Display name '☁️BizyAir JanusModelLoader' might differ from the native display name.
[2025-07-24 10:51:30.531] Display name '☁️BizyAir JanusImageUnderstanding' might differ from the native display name.
[2025-07-24 10:51:30.531] Display name '☁️BizyAir JanusImageGeneration' might differ from the native display name.
[2025-07-24 10:51:30.533] Display name '☁️BizyAir ModelSamplingSD3' might differ from the native display name.
[2025-07-24 10:51:30.533] Display name '☁️BizyAir ModelSamplingFlux' might differ from the native display name.
[2025-07-24 10:51:30.536] Display name '☁️BizyAir NunchakuPulidApply' might differ from the native display name.
[2025-07-24 10:51:30.536] Display name '☁️BizyAir NunchakuPulidLoader' might differ from the native display name.
[2025-07-24 10:51:30.541] Display name '☁️BizyAir TripleCLIPLoader' might differ from the native display name.
[2025-07-24 10:51:30.547] Display name '☁️BizyAir UltimateSDUpscale' might differ from the native display name.
[2025-07-24 10:51:30.547] Display name '☁️BizyAir Wan_LoraLoader' might differ from the native display name.
[2025-07-24 10:51:30.547] Display name '☁️BizyAir Wan_ImageToVideoPipeline' might differ from the native display name.
[2025-07-24 10:51:30.554] Display name '☁️BizyAir Wan_Model_Loader' might differ from the native display name.
[2025-07-24 10:51:30.554] Display name '☁️BizyAir Wan_T2V_Pipeline' might differ from the native display name.
[2025-07-24 10:51:30.561] Display name '☁️BizyAir LoraLoader_Legacy' might differ from the native display name.
[2025-07-24 10:51:30.563] Display name '☁️BizyAir ControlNetLoader_Legacy' might differ from the native display name.
[2025-07-24 10:51:30.563] Display name '☁️BizyAir SamplerCustomAdvanced' might differ from the native display name.
[2025-07-24 10:51:30.563] Display name '☁️BizyAir BasicGuider' might differ from the native display name.
[2025-07-24 10:51:30.563] Display name '☁️BizyAir BasicScheduler' might differ from the native display name.
[2025-07-24 10:51:30.566] Display name '☁️BizyAir DualCLIPLoader' might differ from the native display name.
[2025-07-24 10:51:30.566] Display name '☁️BizyAir KSamplerSelect' might differ from the native display name.
[2025-07-24 10:51:30.566] Display name '☁️BizyAir RandomNoise' might differ from the native display name.
[2025-07-24 10:51:30.568] Display name '☁️BizyAir InpaintModelConditioning' might differ from the native display name.
[2025-07-24 10:51:30.568] Display name '☁️BizyAir InpaintModelConditioning_v2' might differ from the native display name.
[2025-07-24 10:51:30.569] Display name '☁️BizyAir ConditioningZeroOut' might differ from the native display name.
[2025-07-24 10:51:30.569] Display name '☁️BizyAir ConditioningSetTimestepRange' might differ from the native display name.
[2025-07-24 10:51:30.569] Display name '☁️BizyAir PassParameter' might differ from the native display name.
[2025-07-24 10:51:31.282] 

[92m[BizyAir][0m Model hosting service initialized.

[2025-07-24 10:51:31.282] 
[2025-07-24 10:51:35.599] [34m[ComfyUI-Easy-Use] server: [0mv1.3.1 [92mLoaded[0m
[2025-07-24 10:51:35.599] [34m[ComfyUI-Easy-Use] web root: [0mC:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-07-24 10:51:35.667] ----------Jake Upgrade Nodes Loaded----------
[2025-07-24 10:51:35.690] ### Loading: ComfyUI-Manager (V3.34.1)
[2025-07-24 10:51:35.692] [ComfyUI-Manager] network_mode: public
[2025-07-24 10:51:35.826] ### ComfyUI Revision: 150 [9a470e07] *DETACHED | Released on '2025-07-21'
[2025-07-24 10:51:35.860] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-24 10:51:35.860] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-24 10:51:35.860] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-24 10:51:35.940] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-24 10:51:35.943] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-24 10:51:35.999] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-24 10:51:36.102] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-24 10:51:36.200] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-24 10:51:36.839] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-07-24 10:51:36.913] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\__init__.py", line 1, in <module>
    from .faceanalysis import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\faceanalysis.py", line 16, in <module>
    raise Exception("Please install either dlib or insightface to use this node.")
Exception: Please install either dlib or insightface to use this node.

[2025-07-24 10:51:36.913] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis module for custom nodes: Please install either dlib or insightface to use this node.
[2025-07-24 10:51:38.121] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-24 10:51:38.122] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-24 10:51:38.828] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-24 10:51:38.828] 
	[3m[93m"Every strike brings me closer to the next home run."[0m[3m - Babe Ruth[0m
[2025-07-24 10:51:38.828] 
[2025-07-24 10:51:38.842] 
Import times for custom nodes:
[2025-07-24 10:51:38.844]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-24 10:51:38.844]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis
[2025-07-24 10:51:38.844]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-24 10:51:38.845]    0.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade
[2025-07-24 10:51:38.845]    0.2 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 10:51:38.845]    0.9 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 10:51:38.845]    1.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-24 10:51:38.846]    1.9 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns
[2025-07-24 10:51:38.846]    4.3 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 10:51:38.847] 
[2025-07-24 10:51:38.847] WARNING: some comfy_extras/ nodes did not import correctly. This may be because they are missing some dependencies.

[2025-07-24 10:51:38.848] IMPORT FAILED: nodes_audio.py
[2025-07-24 10:51:38.848] 
This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
[2025-07-24 10:51:38.848] Please run the update script: update/update_comfyui.bat
[2025-07-24 10:51:38.848] 
[2025-07-24 10:51:39.366] Context impl SQLiteImpl.
[2025-07-24 10:51:39.366] Will assume non-transactional DDL.
[2025-07-24 10:51:39.366] No target revision found.
[2025-07-24 10:51:39.405] Starting server

[2025-07-24 10:51:39.406] To see the GUI go to: http://127.0.0.1:8188
[2025-07-24 10:51:41.712] FETCH ComfyRegistry Data: 5/92
[2025-07-24 10:51:42.086] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\bizyengine\core\common\client.py:69: UserWarning: invalid api_key
  warnings.warn("invalid api_key")
[2025-07-24 10:51:47.788] FETCH ComfyRegistry Data: 10/92
[2025-07-24 10:51:53.800] FETCH ComfyRegistry Data: 15/92
[2025-07-24 10:52:00.312] FETCH ComfyRegistry Data: 20/92
[2025-07-24 10:52:05.267] FETCH ComfyRegistry Data: 25/92
[2025-07-24 10:52:10.616] FETCH ComfyRegistry Data: 30/92
[2025-07-24 10:52:16.071] FETCH ComfyRegistry Data: 35/92
[2025-07-24 10:52:21.979] FETCH ComfyRegistry Data: 40/92
[2025-07-24 10:52:27.060] FETCH ComfyRegistry Data: 45/92
[2025-07-24 10:52:32.572] FETCH ComfyRegistry Data: 50/92
[2025-07-24 10:52:37.467] FETCH ComfyRegistry Data: 55/92
[2025-07-24 10:52:42.647] FETCH ComfyRegistry Data: 60/92
[2025-07-24 10:52:48.230] FETCH ComfyRegistry Data: 65/92
[2025-07-24 10:52:53.251] FETCH ComfyRegistry Data: 70/92
[2025-07-24 10:52:59.080] FETCH ComfyRegistry Data: 75/92
[2025-07-24 10:53:04.714] FETCH ComfyRegistry Data: 80/92
[2025-07-24 10:53:09.956] FETCH ComfyRegistry Data: 85/92
[2025-07-24 10:53:16.458] FETCH ComfyRegistry Data: 90/92
[2025-07-24 10:53:19.070] FETCH ComfyRegistry Data [DONE]
[2025-07-24 10:53:19.315] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-24 10:53:19.355] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-24 10:53:19.613] [ComfyUI-Manager] All startup tasks have been completed.
[2025-07-24 10:55:17.183] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json [DONE]
[2025-07-24 10:59:18.847] got prompt
[2025-07-24 10:59:18.849] invalid prompt: {'type': 'invalid_prompt', 'message': 'Cannot execute because a node is missing the class_type property.', 'details': "Node ID '#7'", 'extra_info': {}}
[2025-07-24 10:59:35.492] got prompt
[2025-07-24 10:59:35.495] Failed to validate prompt for output 13:
[2025-07-24 10:59:35.496] * VAEEncode 10:
[2025-07-24 10:59:35.496]   - Required input is missing: pixels
[2025-07-24 10:59:35.496] * LoadImage 1:
[2025-07-24 10:59:35.496]   - Custom validation failed for node: image - Invalid image file: image
[2025-07-24 10:59:35.496] * Canny 5:
[2025-07-24 10:59:35.496]   - Value 100.0 bigger than max of 0.99: low_threshold
[2025-07-24 10:59:35.496]   - Value 200.0 bigger than max of 0.99: high_threshold
[2025-07-24 10:59:35.496] * KSampler 11:
[2025-07-24 10:59:35.496]   - Return type mismatch between linked nodes: negative, received_type(MODEL) mismatch input_type(CONDITIONING)
[2025-07-24 10:59:35.496] Output will be ignored
[2025-07-24 10:59:35.496] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 10:59:51.293] got prompt
[2025-07-24 10:59:51.293] invalid prompt: {'type': 'invalid_prompt', 'message': 'Cannot execute because a node is missing the class_type property.', 'details': "Node ID '#7'", 'extra_info': {}}
[2025-07-24 10:59:52.351] got prompt
[2025-07-24 10:59:52.351] invalid prompt: {'type': 'invalid_prompt', 'message': 'Cannot execute because a node is missing the class_type property.', 'details': "Node ID '#7'", 'extra_info': {}}
