{"last_node_id": 10, "last_link_id": 12, "nodes": [{"id": 1, "type": "LoadImage", "pos": [50, 50], "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1, 2], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["image"]}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [400, 50], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [3], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [4, 5], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [6, 7], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["DreamShaper_8_pruned.safetensors"]}, {"id": 3, "type": "IPAdapterUnifiedLoader", "pos": [750, 50], "size": {"0": 315, "1": 78}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 3}], "outputs": [{"name": "model", "type": "MODEL", "links": [8], "slot_index": 0}, {"name": "ipadapter", "type": "IPADAPTER", "links": [9], "slot_index": 1}], "properties": {"Node name for S&R": "IPAdapterUnifiedLoader"}, "widgets_values": ["PLUS (high strength)"]}, {"id": 4, "type": "IPAdapterSimple", "pos": [400, 400], "size": {"0": 315, "1": 200}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 8}, {"name": "ipadapter", "type": "IPADAPTER", "link": 9}, {"name": "image", "type": "IMAGE", "link": 1}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterSimple"}, "widgets_values": [0.8, 0.0, 1.0, "standard"]}, {"id": 5, "type": "CLIPTextEncode", "pos": [750, 400], "size": {"0": 315, "1": 98}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 4}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [11], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cartoon character, 3D animation style, colorful, smooth"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [1100, 400], "size": {"0": 315, "1": 98}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["realistic, photorealistic, blurry, low quality"]}, {"id": 7, "type": "VAEEncode", "pos": [50, 750], "size": {"0": 315, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 2}, {"name": "vae", "type": "VAE", "link": 6}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [13], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}}, {"id": 8, "type": "K<PERSON><PERSON><PERSON>", "pos": [400, 750], "size": {"0": 315, "1": 262}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 10}, {"name": "positive", "type": "CONDITIONING", "link": 11}, {"name": "negative", "type": "CONDITIONING", "link": 12}, {"name": "latent_image", "type": "LATENT", "link": 13}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [14], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 10, 5.5, "euler_a", "normal", 0.6]}, {"id": 9, "type": "VAEDecode", "pos": [750, 750], "size": {"0": 315, "1": 46}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 14}, {"name": "vae", "type": "VAE", "link": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [15], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 10, "type": "SaveImage", "pos": [1100, 750], "size": {"0": 315, "1": 270}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 15}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["Ultra_Fast_Cartoon"]}], "links": [[1, 1, 0, 4, 2, "IMAGE"], [2, 1, 0, 7, 0, "IMAGE"], [3, 2, 0, 3, 0, "MODEL"], [4, 2, 1, 5, 0, "CLIP"], [5, 2, 1, 6, 0, "CLIP"], [6, 2, 2, 7, 1, "VAE"], [7, 2, 2, 9, 1, "VAE"], [8, 3, 0, 4, 0, "MODEL"], [9, 3, 1, 4, 1, "IPADAPTER"], [10, 4, 0, 8, 0, "MODEL"], [11, 5, 0, 8, 1, "CONDITIONING"], [12, 6, 0, 8, 2, "CONDITIONING"], [13, 7, 0, 8, 3, "LATENT"], [14, 8, 0, 9, 0, "LATENT"], [15, 9, 0, 10, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}