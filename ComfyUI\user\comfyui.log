## ComfyUI-Manager: installing dependencies done.
[2025-07-25 10:01:24.408] ** ComfyUI startup time: 2025-07-25 10:01:24.408
[2025-07-25 10:01:24.410] ** Platform: Windows
[2025-07-25 10:01:24.412] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-25 10:01:24.412] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-25 10:01:24.414] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-25 10:01:24.427] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-25 10:01:24.429] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-25 10:01:24.429] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-25 10:01:24.429] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-25 10:01:27.859]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-25 10:01:27.859]    1.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-25 10:01:27.859]    6.9 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-25 10:01:27.859] 
[2025-07-25 10:01:30.787] Checkpoint files will always be loaded safely.
[2025-07-25 10:01:30.822] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-25 10:01:30.822] pytorch version: 2.4.1+cpu
[2025-07-25 10:01:30.823] Set vram state to: DISABLED
[2025-07-25 10:01:30.824] Device: cpu
[2025-07-25 10:01:35.527] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-25 10:01:35.588] torchaudio missing, ACE model will be broken
[2025-07-25 10:01:35.588] torchaudio missing, ACE model will be broken
[2025-07-25 10:02:03.536] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-25 10:02:03.536] ComfyUI version: 0.3.45
[2025-07-25 10:02:03.591] ComfyUI frontend version: 1.23.4
[2025-07-25 10:02:03.593] [Prompt Server] web root: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-07-25 10:02:06.276] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py", line 4, in <module>
    import torchaudio
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\__init__.py", line 2, in <module>
    from . import _extension  # noqa  # usort: skip
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\__init__.py", line 38, in <module>
    _load_lib("libtorchaudio")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\utils.py", line 60, in _load_lib
    torch.ops.load_library(path)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\_ops.py", line 1295, in load_library
    ctypes.CDLL(path)
  File "ctypes\__init__.py", line 379, in __init__
FileNotFoundError: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.

[2025-07-25 10:02:06.277] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py module for custom nodes: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
[2025-07-25 10:02:06.955] [92m[BizyAir][0m BizyAir ComfyUI Plugin: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-25 10:02:07.074] Display name '☁️BizyAir CogView4_6B_Pipe' might differ from the native display name.
[2025-07-25 10:02:07.084] Display name '☁️BizyAir SetUnionControlNetType' might differ from the native display name.
[2025-07-25 10:02:07.084] Display name '☁️BizyAir ControlNetInpaintingAliMamaApply' might differ from the native display name.
[2025-07-25 10:02:07.087] Display name '☁️BizyAir DisableNoise' might differ from the native display name.
[2025-07-25 10:02:07.088] Display name '☁️BizyAir VPScheduler' might differ from the native display name.
[2025-07-25 10:02:07.088] Display name '☁️BizyAir SplitSigmas' might differ from the native display name.
[2025-07-25 10:02:07.088] Display name '☁️BizyAir SplitSigmasDenoise' might differ from the native display name.
[2025-07-25 10:02:07.089] Display name '☁️BizyAir FlipSigmas' might differ from the native display name.
[2025-07-25 10:02:07.089] Display name '☁️BizyAir CFGGuider' might differ from the native display name.
[2025-07-25 10:02:07.091] Display name '☁️BizyAir TrainDatasetAdd' might differ from the native display name.
[2025-07-25 10:02:07.095] Display name '☁️BizyAir CLIPTextEncodeFlux' might differ from the native display name.
[2025-07-25 10:02:07.096] Display name '☁️BizyAir ReferenceLatent' might differ from the native display name.
[2025-07-25 10:02:07.096] Display name '☁️BizyAir FluxGuidance' might differ from the native display name.
[2025-07-25 10:02:07.100] Display name '☁️BizyAir Hy3D_2_1SimpleMeshGen' might differ from the native display name.
[2025-07-25 10:02:07.100] Display name '☁️BizyAir Hy3DExportMesh' might differ from the native display name.
[2025-07-25 10:02:07.103] Display name '☁️BizyAir InstructPixToPixConditioning' might differ from the native display name.
[2025-07-25 10:02:07.106] Display name '☁️BizyAir IPAdapterModelLoader' might differ from the native display name.
[2025-07-25 10:02:07.107] Display name '☁️BizyAir IPAdapterAdvanced' might differ from the native display name.
[2025-07-25 10:02:07.107] Display name '☁️BizyAir IPAdapterStyleComposition' might differ from the native display name.
[2025-07-25 10:02:07.109] Display name '☁️BizyAir JanusModelLoader' might differ from the native display name.
[2025-07-25 10:02:07.109] Display name '☁️BizyAir JanusImageUnderstanding' might differ from the native display name.
[2025-07-25 10:02:07.109] Display name '☁️BizyAir JanusImageGeneration' might differ from the native display name.
[2025-07-25 10:02:07.114] Display name '☁️BizyAir ModelSamplingSD3' might differ from the native display name.
[2025-07-25 10:02:07.114] Display name '☁️BizyAir ModelSamplingFlux' might differ from the native display name.
[2025-07-25 10:02:07.117] Display name '☁️BizyAir NunchakuPulidApply' might differ from the native display name.
[2025-07-25 10:02:07.118] Display name '☁️BizyAir NunchakuPulidLoader' might differ from the native display name.
[2025-07-25 10:02:07.123] Display name '☁️BizyAir TripleCLIPLoader' might differ from the native display name.
[2025-07-25 10:02:07.133] Display name '☁️BizyAir UltimateSDUpscale' might differ from the native display name.
[2025-07-25 10:02:07.139] Display name '☁️BizyAir Wan_LoraLoader' might differ from the native display name.
[2025-07-25 10:02:07.140] Display name '☁️BizyAir Wan_ImageToVideoPipeline' might differ from the native display name.
[2025-07-25 10:02:07.142] Display name '☁️BizyAir Wan_Model_Loader' might differ from the native display name.
[2025-07-25 10:02:07.142] Display name '☁️BizyAir Wan_T2V_Pipeline' might differ from the native display name.
[2025-07-25 10:02:07.153] Display name '☁️BizyAir LoraLoader_Legacy' might differ from the native display name.
[2025-07-25 10:02:07.153] Display name '☁️BizyAir ControlNetLoader_Legacy' might differ from the native display name.
[2025-07-25 10:02:07.154] Display name '☁️BizyAir SamplerCustomAdvanced' might differ from the native display name.
[2025-07-25 10:02:07.155] Display name '☁️BizyAir BasicGuider' might differ from the native display name.
[2025-07-25 10:02:07.155] Display name '☁️BizyAir BasicScheduler' might differ from the native display name.
[2025-07-25 10:02:07.155] Display name '☁️BizyAir DualCLIPLoader' might differ from the native display name.
[2025-07-25 10:02:07.155] Display name '☁️BizyAir KSamplerSelect' might differ from the native display name.
[2025-07-25 10:02:07.156] Display name '☁️BizyAir RandomNoise' might differ from the native display name.
[2025-07-25 10:02:07.156] Display name '☁️BizyAir InpaintModelConditioning' might differ from the native display name.
[2025-07-25 10:02:07.156] Display name '☁️BizyAir InpaintModelConditioning_v2' might differ from the native display name.
[2025-07-25 10:02:07.156] Display name '☁️BizyAir ConditioningZeroOut' might differ from the native display name.
[2025-07-25 10:02:07.158] Display name '☁️BizyAir ConditioningSetTimestepRange' might differ from the native display name.
[2025-07-25 10:02:07.158] Display name '☁️BizyAir PassParameter' might differ from the native display name.
[2025-07-25 10:02:08.509] 

[92m[BizyAir][0m Model hosting service initialized.

[2025-07-25 10:02:08.509] 
[2025-07-25 10:02:18.062] [34m[ComfyUI-Easy-Use] server: [0mv1.3.1 [92mLoaded[0m
[2025-07-25 10:02:18.062] [34m[ComfyUI-Easy-Use] web root: [0mC:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-07-25 10:02:18.206] ----------Jake Upgrade Nodes Loaded----------
[2025-07-25 10:02:18.262] ### Loading: ComfyUI-Manager (V3.34.1)
[2025-07-25 10:02:18.264] [ComfyUI-Manager] network_mode: public
[2025-07-25 10:02:18.498] ### ComfyUI Revision: 150 [9a470e07] *DETACHED | Released on '2025-07-21'
[2025-07-25 10:02:18.786] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-25 10:02:18.787] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-25 10:02:18.790] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-25 10:02:18.868] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-25 10:02:18.986] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-25 10:02:19.076] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-25 10:02:19.083] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-25 10:02:19.333] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-25 10:02:20.440] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-07-25 10:02:20.556] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\__init__.py", line 1, in <module>
    from .faceanalysis import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\faceanalysis.py", line 16, in <module>
    raise Exception("Please install either dlib or insightface to use this node.")
Exception: Please install either dlib or insightface to use this node.

[2025-07-25 10:02:20.556] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis module for custom nodes: Please install either dlib or insightface to use this node.
[2025-07-25 10:02:22.587] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-25 10:02:22.587] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-25 10:02:24.343] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-25 10:02:24.344] 
	[3m[93m"Opportunities don't happen. You create them."[0m[3m - Chris Grosser[0m
[2025-07-25 10:02:24.345] 
[2025-07-25 10:02:24.366] 
Import times for custom nodes:
[2025-07-25 10:02:24.367]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-25 10:02:24.367]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-25 10:02:24.368]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis
[2025-07-25 10:02:24.368]    0.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade
[2025-07-25 10:02:24.369]    0.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-25 10:02:24.369]    1.6 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-25 10:02:24.369]    1.8 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-25 10:02:24.369]    3.8 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns
[2025-07-25 10:02:24.371]    9.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-25 10:02:24.371] 
[2025-07-25 10:02:24.371] WARNING: some comfy_extras/ nodes did not import correctly. This may be because they are missing some dependencies.

[2025-07-25 10:02:24.372] IMPORT FAILED: nodes_audio.py
[2025-07-25 10:02:24.372] 
This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
[2025-07-25 10:02:24.372] Please run the update script: update/update_comfyui.bat
[2025-07-25 10:02:24.372] 
[2025-07-25 10:02:24.588] FETCH ComfyRegistry Data: 5/92
[2025-07-25 10:02:26.026] Context impl SQLiteImpl.
[2025-07-25 10:02:26.026] Will assume non-transactional DDL.
[2025-07-25 10:02:26.026] No target revision found.
[2025-07-25 10:02:26.073] Starting server

[2025-07-25 10:02:26.074] To see the GUI go to: http://127.0.0.1:8188
