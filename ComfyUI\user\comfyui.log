## ComfyUI-Manager: installing dependencies done.
[2025-07-25 10:01:24.408] ** ComfyUI startup time: 2025-07-25 10:01:24.408
[2025-07-25 10:01:24.410] ** Platform: Windows
[2025-07-25 10:01:24.412] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-25 10:01:24.412] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-25 10:01:24.414] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-25 10:01:24.427] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-25 10:01:24.429] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-25 10:01:24.429] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-25 10:01:24.429] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-25 10:01:27.859]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-25 10:01:27.859]    1.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-25 10:01:27.859]    6.9 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-25 10:01:27.859] 
[2025-07-25 10:01:30.787] Checkpoint files will always be loaded safely.
[2025-07-25 10:01:30.822] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-25 10:01:30.822] pytorch version: 2.4.1+cpu
[2025-07-25 10:01:30.823] Set vram state to: DISABLED
[2025-07-25 10:01:30.824] Device: cpu
[2025-07-25 10:01:35.527] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-25 10:01:35.588] torchaudio missing, ACE model will be broken
[2025-07-25 10:01:35.588] torchaudio missing, ACE model will be broken
[2025-07-25 10:02:03.536] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-25 10:02:03.536] ComfyUI version: 0.3.45
[2025-07-25 10:02:03.591] ComfyUI frontend version: 1.23.4
[2025-07-25 10:02:03.593] [Prompt Server] web root: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-07-25 10:02:06.276] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py", line 4, in <module>
    import torchaudio
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\__init__.py", line 2, in <module>
    from . import _extension  # noqa  # usort: skip
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\__init__.py", line 38, in <module>
    _load_lib("libtorchaudio")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\utils.py", line 60, in _load_lib
    torch.ops.load_library(path)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\_ops.py", line 1295, in load_library
    ctypes.CDLL(path)
  File "ctypes\__init__.py", line 379, in __init__
FileNotFoundError: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.

[2025-07-25 10:02:06.277] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py module for custom nodes: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
[2025-07-25 10:02:06.955] [92m[BizyAir][0m BizyAir ComfyUI Plugin: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-25 10:02:07.074] Display name '☁️BizyAir CogView4_6B_Pipe' might differ from the native display name.
[2025-07-25 10:02:07.084] Display name '☁️BizyAir SetUnionControlNetType' might differ from the native display name.
[2025-07-25 10:02:07.084] Display name '☁️BizyAir ControlNetInpaintingAliMamaApply' might differ from the native display name.
[2025-07-25 10:02:07.087] Display name '☁️BizyAir DisableNoise' might differ from the native display name.
[2025-07-25 10:02:07.088] Display name '☁️BizyAir VPScheduler' might differ from the native display name.
[2025-07-25 10:02:07.088] Display name '☁️BizyAir SplitSigmas' might differ from the native display name.
[2025-07-25 10:02:07.088] Display name '☁️BizyAir SplitSigmasDenoise' might differ from the native display name.
[2025-07-25 10:02:07.089] Display name '☁️BizyAir FlipSigmas' might differ from the native display name.
[2025-07-25 10:02:07.089] Display name '☁️BizyAir CFGGuider' might differ from the native display name.
[2025-07-25 10:02:07.091] Display name '☁️BizyAir TrainDatasetAdd' might differ from the native display name.
[2025-07-25 10:02:07.095] Display name '☁️BizyAir CLIPTextEncodeFlux' might differ from the native display name.
[2025-07-25 10:02:07.096] Display name '☁️BizyAir ReferenceLatent' might differ from the native display name.
[2025-07-25 10:02:07.096] Display name '☁️BizyAir FluxGuidance' might differ from the native display name.
[2025-07-25 10:02:07.100] Display name '☁️BizyAir Hy3D_2_1SimpleMeshGen' might differ from the native display name.
[2025-07-25 10:02:07.100] Display name '☁️BizyAir Hy3DExportMesh' might differ from the native display name.
[2025-07-25 10:02:07.103] Display name '☁️BizyAir InstructPixToPixConditioning' might differ from the native display name.
[2025-07-25 10:02:07.106] Display name '☁️BizyAir IPAdapterModelLoader' might differ from the native display name.
[2025-07-25 10:02:07.107] Display name '☁️BizyAir IPAdapterAdvanced' might differ from the native display name.
[2025-07-25 10:02:07.107] Display name '☁️BizyAir IPAdapterStyleComposition' might differ from the native display name.
[2025-07-25 10:02:07.109] Display name '☁️BizyAir JanusModelLoader' might differ from the native display name.
[2025-07-25 10:02:07.109] Display name '☁️BizyAir JanusImageUnderstanding' might differ from the native display name.
[2025-07-25 10:02:07.109] Display name '☁️BizyAir JanusImageGeneration' might differ from the native display name.
[2025-07-25 10:02:07.114] Display name '☁️BizyAir ModelSamplingSD3' might differ from the native display name.
[2025-07-25 10:02:07.114] Display name '☁️BizyAir ModelSamplingFlux' might differ from the native display name.
[2025-07-25 10:02:07.117] Display name '☁️BizyAir NunchakuPulidApply' might differ from the native display name.
[2025-07-25 10:02:07.118] Display name '☁️BizyAir NunchakuPulidLoader' might differ from the native display name.
[2025-07-25 10:02:07.123] Display name '☁️BizyAir TripleCLIPLoader' might differ from the native display name.
[2025-07-25 10:02:07.133] Display name '☁️BizyAir UltimateSDUpscale' might differ from the native display name.
[2025-07-25 10:02:07.139] Display name '☁️BizyAir Wan_LoraLoader' might differ from the native display name.
[2025-07-25 10:02:07.140] Display name '☁️BizyAir Wan_ImageToVideoPipeline' might differ from the native display name.
[2025-07-25 10:02:07.142] Display name '☁️BizyAir Wan_Model_Loader' might differ from the native display name.
[2025-07-25 10:02:07.142] Display name '☁️BizyAir Wan_T2V_Pipeline' might differ from the native display name.
[2025-07-25 10:02:07.153] Display name '☁️BizyAir LoraLoader_Legacy' might differ from the native display name.
[2025-07-25 10:02:07.153] Display name '☁️BizyAir ControlNetLoader_Legacy' might differ from the native display name.
[2025-07-25 10:02:07.154] Display name '☁️BizyAir SamplerCustomAdvanced' might differ from the native display name.
[2025-07-25 10:02:07.155] Display name '☁️BizyAir BasicGuider' might differ from the native display name.
[2025-07-25 10:02:07.155] Display name '☁️BizyAir BasicScheduler' might differ from the native display name.
[2025-07-25 10:02:07.155] Display name '☁️BizyAir DualCLIPLoader' might differ from the native display name.
[2025-07-25 10:02:07.155] Display name '☁️BizyAir KSamplerSelect' might differ from the native display name.
[2025-07-25 10:02:07.156] Display name '☁️BizyAir RandomNoise' might differ from the native display name.
[2025-07-25 10:02:07.156] Display name '☁️BizyAir InpaintModelConditioning' might differ from the native display name.
[2025-07-25 10:02:07.156] Display name '☁️BizyAir InpaintModelConditioning_v2' might differ from the native display name.
[2025-07-25 10:02:07.156] Display name '☁️BizyAir ConditioningZeroOut' might differ from the native display name.
[2025-07-25 10:02:07.158] Display name '☁️BizyAir ConditioningSetTimestepRange' might differ from the native display name.
[2025-07-25 10:02:07.158] Display name '☁️BizyAir PassParameter' might differ from the native display name.
[2025-07-25 10:02:08.509] 

[92m[BizyAir][0m Model hosting service initialized.

[2025-07-25 10:02:08.509] 
[2025-07-25 10:02:18.062] [34m[ComfyUI-Easy-Use] server: [0mv1.3.1 [92mLoaded[0m
[2025-07-25 10:02:18.062] [34m[ComfyUI-Easy-Use] web root: [0mC:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-07-25 10:02:18.206] ----------Jake Upgrade Nodes Loaded----------
[2025-07-25 10:02:18.262] ### Loading: ComfyUI-Manager (V3.34.1)
[2025-07-25 10:02:18.264] [ComfyUI-Manager] network_mode: public
[2025-07-25 10:02:18.498] ### ComfyUI Revision: 150 [9a470e07] *DETACHED | Released on '2025-07-21'
[2025-07-25 10:02:18.786] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-25 10:02:18.787] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-25 10:02:18.790] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-25 10:02:18.868] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-25 10:02:18.986] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-25 10:02:19.076] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-25 10:02:19.083] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-25 10:02:19.333] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-25 10:02:20.440] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-07-25 10:02:20.556] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\__init__.py", line 1, in <module>
    from .faceanalysis import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\faceanalysis.py", line 16, in <module>
    raise Exception("Please install either dlib or insightface to use this node.")
Exception: Please install either dlib or insightface to use this node.

[2025-07-25 10:02:20.556] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis module for custom nodes: Please install either dlib or insightface to use this node.
[2025-07-25 10:02:22.587] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-25 10:02:22.587] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-25 10:02:24.343] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-25 10:02:24.344] 
	[3m[93m"Opportunities don't happen. You create them."[0m[3m - Chris Grosser[0m
[2025-07-25 10:02:24.345] 
[2025-07-25 10:02:24.366] 
Import times for custom nodes:
[2025-07-25 10:02:24.367]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-25 10:02:24.367]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-25 10:02:24.368]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis
[2025-07-25 10:02:24.368]    0.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade
[2025-07-25 10:02:24.369]    0.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-25 10:02:24.369]    1.6 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-25 10:02:24.369]    1.8 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-25 10:02:24.369]    3.8 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns
[2025-07-25 10:02:24.371]    9.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-25 10:02:24.371] 
[2025-07-25 10:02:24.371] WARNING: some comfy_extras/ nodes did not import correctly. This may be because they are missing some dependencies.

[2025-07-25 10:02:24.372] IMPORT FAILED: nodes_audio.py
[2025-07-25 10:02:24.372] 
This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
[2025-07-25 10:02:24.372] Please run the update script: update/update_comfyui.bat
[2025-07-25 10:02:24.372] 
[2025-07-25 10:02:24.588] FETCH ComfyRegistry Data: 5/92
[2025-07-25 10:02:26.026] Context impl SQLiteImpl.
[2025-07-25 10:02:26.026] Will assume non-transactional DDL.
[2025-07-25 10:02:26.026] No target revision found.
[2025-07-25 10:02:26.073] Starting server

[2025-07-25 10:02:26.074] To see the GUI go to: http://127.0.0.1:8188
[2025-07-25 10:02:28.878] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\bizyengine\core\common\client.py:69: UserWarning: invalid api_key
  warnings.warn("invalid api_key")
[2025-07-25 10:02:29.805] FETCH ComfyRegistry Data: 10/92
[2025-07-25 10:02:35.236] FETCH ComfyRegistry Data: 15/92
[2025-07-25 10:02:37.673] got prompt
[2025-07-25 10:02:37.674] invalid prompt: {'type': 'invalid_prompt', 'message': 'Cannot execute because a node is missing the class_type property.', 'details': "Node ID '#16'", 'extra_info': {}}
[2025-07-25 10:02:40.878] FETCH ComfyRegistry Data: 20/92
[2025-07-25 10:02:46.155] FETCH ComfyRegistry Data: 25/92
[2025-07-25 10:02:51.926] FETCH ComfyRegistry Data: 30/92
[2025-07-25 10:02:59.002] FETCH ComfyRegistry Data: 35/92
[2025-07-25 10:03:04.647] FETCH ComfyRegistry Data: 40/92
[2025-07-25 10:03:09.815] FETCH ComfyRegistry Data: 45/92
[2025-07-25 10:03:11.776] got prompt
[2025-07-25 10:03:11.777] Failed to validate prompt for output 15:
[2025-07-25 10:03:11.777] * IPAdapterFaceID 11:
[2025-07-25 10:03:11.777]   - Value not in list: combine_embeds: '0' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-25 10:03:11.777] Output will be ignored
[2025-07-25 10:03:11.777] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-25 10:03:15.380] FETCH ComfyRegistry Data: 50/92
[2025-07-25 10:03:20.296] FETCH ComfyRegistry Data: 55/92
[2025-07-25 10:03:24.682] got prompt
[2025-07-25 10:03:24.692] Failed to validate prompt for output 15:
[2025-07-25 10:03:24.694] * IPAdapterFaceID 11:
[2025-07-25 10:03:24.694]   - Value not in list: combine_embeds: '0' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-25 10:03:24.694] Output will be ignored
[2025-07-25 10:03:24.694] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-25 10:03:25.720] FETCH ComfyRegistry Data: 60/92
[2025-07-25 10:03:30.857] FETCH ComfyRegistry Data: 65/92
[2025-07-25 10:03:34.501] got prompt
[2025-07-25 10:03:34.504] Failed to validate prompt for output 15:
[2025-07-25 10:03:34.504] * IPAdapterFaceID 11:
[2025-07-25 10:03:34.504]   - Value not in list: combine_embeds: '0' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-25 10:03:34.504] Output will be ignored
[2025-07-25 10:03:34.504] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-25 10:03:36.300] FETCH ComfyRegistry Data: 70/92
[2025-07-25 10:03:42.772] FETCH ComfyRegistry Data: 75/92
[2025-07-25 10:03:47.482] got prompt
[2025-07-25 10:03:48.024] model weight dtype torch.float32, manual cast: None
[2025-07-25 10:03:48.048] model_type EPS
[2025-07-25 10:03:49.853] FETCH ComfyRegistry Data: 80/92
[2025-07-25 10:03:52.723] Using split attention in VAE
[2025-07-25 10:03:52.734] Using split attention in VAE
[2025-07-25 10:03:53.699] VAE load device: cpu, offload device: cpu, dtype: torch.float32
[2025-07-25 10:03:54.644] Requested to load SD1ClipModel
[2025-07-25 10:03:54.663] loaded completely 9.5367431640625e+25 235.84423828125 True
[2025-07-25 10:03:54.669] CLIP/text encoder model load device: cpu, offload device: cpu, current: cpu, dtype: torch.float16
[2025-07-25 10:03:55.494] FETCH ComfyRegistry Data: 85/92
[2025-07-25 10:03:57.247] Requested to load AutoencoderKL
[2025-07-25 10:03:57.352] loaded completely 9.5367431640625e+25 319.11416244506836 True
[2025-07-25 10:04:02.082] FETCH ComfyRegistry Data: 90/92
[2025-07-25 10:04:05.488] FETCH ComfyRegistry Data [DONE]
[2025-07-25 10:04:07.120] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-25 10:04:07.470] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-25 10:04:08.094] [ComfyUI-Manager] All startup tasks have been completed.
[2025-07-25 10:05:55.374] [33mINFO: Clip Vision model loaded from C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\models\clip_vision\CLIP-ViT-H-14-laion2B-s32B-b79K.safetensors[0m
[2025-07-25 10:05:55.377] !!! Exception during processing !!! IPAdapter model not found.
[2025-07-25 10:05:55.405] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 515, in load_models
    raise Exception("IPAdapter model not found.")
Exception: IPAdapter model not found.

[2025-07-25 10:05:55.413] Prompt executed in 127.92 seconds
[2025-07-25 10:07:12.570] got prompt
[2025-07-25 10:07:13.267] !!! Exception during processing !!! IPAdapter model not found.
[2025-07-25 10:07:13.269] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 515, in load_models
    raise Exception("IPAdapter model not found.")
Exception: IPAdapter model not found.

[2025-07-25 10:07:13.274] Prompt executed in 0.70 seconds
[2025-07-25 10:07:24.763] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json [DONE]
[2025-07-25 10:08:21.601] got prompt
[2025-07-25 10:08:21.953] !!! Exception during processing !!! IPAdapter model not found.
[2025-07-25 10:08:22.017] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 515, in load_models
    raise Exception("IPAdapter model not found.")
Exception: IPAdapter model not found.

[2025-07-25 10:08:22.120] Prompt executed in 0.44 seconds
[2025-07-25 10:13:12.710] got prompt
[2025-07-25 10:13:12.717] Failed to validate prompt for output 15:
[2025-07-25 10:13:12.717] * IPAdapterFaceID 11:
[2025-07-25 10:13:12.717]   - Value not in list: combine_embeds: '0' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-25 10:13:12.718] Output will be ignored
[2025-07-25 10:13:12.718] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-25 10:13:28.225] got prompt
[2025-07-25 10:13:29.016] !!! Exception during processing !!! Missing CLIPVision model.
[2025-07-25 10:13:29.018] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 708, in apply_ipadapter
    raise Exception("Missing CLIPVision model.")
Exception: Missing CLIPVision model.

[2025-07-25 10:13:29.022] Prompt executed in 0.79 seconds
[2025-07-25 10:13:39.888] got prompt
[2025-07-25 10:13:40.258] !!! Exception during processing !!! Missing CLIPVision model.
[2025-07-25 10:13:40.259] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 708, in apply_ipadapter
    raise Exception("Missing CLIPVision model.")
Exception: Missing CLIPVision model.

[2025-07-25 10:13:40.264] Prompt executed in 0.37 seconds
[2025-07-25 10:13:56.856] got prompt
[2025-07-25 10:13:57.122] !!! Exception during processing !!! Missing CLIPVision model.
[2025-07-25 10:13:57.124] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 708, in apply_ipadapter
    raise Exception("Missing CLIPVision model.")
Exception: Missing CLIPVision model.

[2025-07-25 10:13:57.127] Prompt executed in 0.27 seconds
[2025-07-25 10:14:07.376] got prompt
[2025-07-25 10:14:07.665] !!! Exception during processing !!! Missing CLIPVision model.
[2025-07-25 10:14:07.667] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 708, in apply_ipadapter
    raise Exception("Missing CLIPVision model.")
Exception: Missing CLIPVision model.

[2025-07-25 10:14:07.670] Prompt executed in 0.29 seconds
[2025-07-25 10:14:29.440] got prompt
[2025-07-25 10:14:29.678] !!! Exception during processing !!! Missing CLIPVision model.
[2025-07-25 10:14:29.680] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 708, in apply_ipadapter
    raise Exception("Missing CLIPVision model.")
Exception: Missing CLIPVision model.

[2025-07-25 10:14:29.682] Prompt executed in 0.24 seconds
[2025-07-25 10:15:16.104] got prompt
[2025-07-25 10:15:16.341] !!! Exception during processing !!! Missing CLIPVision model.
[2025-07-25 10:15:16.341] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 708, in apply_ipadapter
    raise Exception("Missing CLIPVision model.")
Exception: Missing CLIPVision model.

[2025-07-25 10:15:16.348] Prompt executed in 0.24 seconds
[2025-07-25 10:18:17.268] got prompt
[2025-07-25 10:18:17.273] Failed to validate prompt for output 15:
[2025-07-25 10:18:17.273] * IPAdapterFaceID 11:
[2025-07-25 10:18:17.273]   - Value not in list: combine_embeds: '0' not in ['concat', 'add', 'subtract', 'average', 'norm average']
[2025-07-25 10:18:17.273] Output will be ignored
[2025-07-25 10:18:17.274] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-25 10:18:27.315] got prompt
[2025-07-25 10:18:30.516] !!! Exception during processing !!! insightface model is required for FaceID models
[2025-07-25 10:18:30.518] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 758, in apply_ipadapter
    work_model, face_image = ipadapter_execute(work_model, ipadapter_model, clip_vision, **ipa_args)
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 237, in ipadapter_execute
    raise Exception("insightface model is required for FaceID models")
Exception: insightface model is required for FaceID models

[2025-07-25 10:18:30.522] Prompt executed in 3.20 seconds
[2025-07-25 10:18:42.444] got prompt
[2025-07-25 10:18:42.718] !!! Exception during processing !!! insightface model is required for FaceID models
[2025-07-25 10:18:42.719] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 758, in apply_ipadapter
    work_model, face_image = ipadapter_execute(work_model, ipadapter_model, clip_vision, **ipa_args)
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus\IPAdapterPlus.py", line 237, in ipadapter_execute
    raise Exception("insightface model is required for FaceID models")
Exception: insightface model is required for FaceID models

[2025-07-25 10:18:42.722] Prompt executed in 0.27 seconds
