## ComfyUI-Manager: installing dependencies done.
[2025-07-25 09:58:00.358] ** ComfyUI startup time: 2025-07-25 09:58:00.358
[2025-07-25 09:58:00.366] ** Platform: Windows
[2025-07-25 09:58:00.366] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-25 09:58:00.367] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-25 09:58:00.367] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-25 09:58:00.367] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-25 09:58:00.368] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-25 09:58:00.368] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-25 09:58:00.368] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-25 09:58:04.511]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-25 09:58:04.511]    7.6 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-25 09:58:04.511]    8.9 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-25 09:58:04.511] 
[2025-07-25 09:58:10.509] Checkpoint files will always be loaded safely.
[2025-07-25 09:58:10.552] Traceback (most recent call last):
[2025-07-25 09:58:10.553]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\main.py", line 138, in <module>
[2025-07-25 09:58:10.554]     import execution
[2025-07-25 09:58:10.555]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 15, in <module>
[2025-07-25 09:58:10.578]     import comfy.model_management
[2025-07-25 09:58:10.579]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\model_management.py", line 221, in <module>
[2025-07-25 09:58:10.601]     total_vram = get_total_memory(get_torch_device()) / (1024 * 1024)
[2025-07-25 09:58:10.602]                                   ^^^^^^^^^^^^^^^^^^
[2025-07-25 09:58:10.620]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy\model_management.py", line 172, in get_torch_device
[2025-07-25 09:58:10.622]     return torch.device(torch.cuda.current_device())
[2025-07-25 09:58:10.622]                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-07-25 09:58:10.636]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\cuda\__init__.py", line 878, in current_device
[2025-07-25 09:58:10.637]     _lazy_init()
[2025-07-25 09:58:10.639]   File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\cuda\__init__.py", line 305, in _lazy_init
[2025-07-25 09:58:10.640]     raise AssertionError("Torch not compiled with CUDA enabled")
[2025-07-25 09:58:10.642] AssertionError: Torch not compiled with CUDA enabled
