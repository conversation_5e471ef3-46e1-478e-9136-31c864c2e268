## ComfyUI-Manager: installing dependencies done.
[2025-07-24 14:30:51.461] ** ComfyUI startup time: 2025-07-24 14:30:51.461
[2025-07-24 14:30:51.461] ** Platform: Windows
[2025-07-24 14:30:51.461] ** Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-24 14:30:51.461] ** Python executable: C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\python.exe
[2025-07-24 14:30:51.466] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 14:30:51.466] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 14:30:51.466] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-24 14:30:51.466] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-24 14:30:51.466] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-24 14:30:54.472]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 14:30:54.472]    1.6 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 14:30:54.472]    5.7 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 14:30:54.472] 
[2025-07-24 14:30:56.983] Checkpoint files will always be loaded safely.
[2025-07-24 14:30:57.002] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-24 14:30:57.002] pytorch version: 2.7.1+cu128
[2025-07-24 14:30:57.013] Set vram state to: DISABLED
[2025-07-24 14:30:57.013] Device: cpu
[2025-07-24 14:31:14.539] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-24 14:31:49.229] Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
[2025-07-24 14:31:49.229] ComfyUI version: 0.3.45
[2025-07-24 14:31:49.297] ComfyUI frontend version: 1.23.4
[2025-07-24 14:31:49.299] [Prompt Server] web root: C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\comfyui_frontend_package\static
[2025-07-24 14:31:59.231] [92m[BizyAir][0m BizyAir ComfyUI Plugin: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 14:31:59.655] Display name '☁️BizyAir CogView4_6B_Pipe' might differ from the native display name.
[2025-07-24 14:31:59.730] Display name '☁️BizyAir SetUnionControlNetType' might differ from the native display name.
[2025-07-24 14:31:59.731] Display name '☁️BizyAir ControlNetInpaintingAliMamaApply' might differ from the native display name.
[2025-07-24 14:31:59.747] Display name '☁️BizyAir DisableNoise' might differ from the native display name.
[2025-07-24 14:31:59.748] Display name '☁️BizyAir VPScheduler' might differ from the native display name.
[2025-07-24 14:31:59.748] Display name '☁️BizyAir SplitSigmas' might differ from the native display name.
[2025-07-24 14:31:59.750] Display name '☁️BizyAir SplitSigmasDenoise' might differ from the native display name.
[2025-07-24 14:31:59.750] Display name '☁️BizyAir FlipSigmas' might differ from the native display name.
[2025-07-24 14:31:59.751] Display name '☁️BizyAir CFGGuider' might differ from the native display name.
[2025-07-24 14:31:59.766] Display name '☁️BizyAir TrainDatasetAdd' might differ from the native display name.
[2025-07-24 14:31:59.793] Display name '☁️BizyAir CLIPTextEncodeFlux' might differ from the native display name.
[2025-07-24 14:31:59.793] Display name '☁️BizyAir ReferenceLatent' might differ from the native display name.
[2025-07-24 14:31:59.795] Display name '☁️BizyAir FluxGuidance' might differ from the native display name.
[2025-07-24 14:31:59.811] Display name '☁️BizyAir Hy3D_2_1SimpleMeshGen' might differ from the native display name.
[2025-07-24 14:31:59.812] Display name '☁️BizyAir Hy3DExportMesh' might differ from the native display name.
[2025-07-24 14:31:59.848] Display name '☁️BizyAir InstructPixToPixConditioning' might differ from the native display name.
[2025-07-24 14:31:59.883] Display name '☁️BizyAir IPAdapterModelLoader' might differ from the native display name.
[2025-07-24 14:31:59.884] Display name '☁️BizyAir IPAdapterAdvanced' might differ from the native display name.
[2025-07-24 14:31:59.884] Display name '☁️BizyAir IPAdapterStyleComposition' might differ from the native display name.
[2025-07-24 14:31:59.904] Display name '☁️BizyAir JanusModelLoader' might differ from the native display name.
[2025-07-24 14:31:59.905] Display name '☁️BizyAir JanusImageUnderstanding' might differ from the native display name.
[2025-07-24 14:31:59.905] Display name '☁️BizyAir JanusImageGeneration' might differ from the native display name.
[2025-07-24 14:31:59.943] Display name '☁️BizyAir ModelSamplingSD3' might differ from the native display name.
[2025-07-24 14:31:59.943] Display name '☁️BizyAir ModelSamplingFlux' might differ from the native display name.
[2025-07-24 14:31:59.957] Display name '☁️BizyAir NunchakuPulidApply' might differ from the native display name.
[2025-07-24 14:31:59.958] Display name '☁️BizyAir NunchakuPulidLoader' might differ from the native display name.
[2025-07-24 14:32:00.006] Display name '☁️BizyAir TripleCLIPLoader' might differ from the native display name.
[2025-07-24 14:32:00.083] Display name '☁️BizyAir UltimateSDUpscale' might differ from the native display name.
[2025-07-24 14:32:00.130] Display name '☁️BizyAir Wan_LoraLoader' might differ from the native display name.
[2025-07-24 14:32:00.131] Display name '☁️BizyAir Wan_ImageToVideoPipeline' might differ from the native display name.
[2025-07-24 14:32:00.145] Display name '☁️BizyAir Wan_Model_Loader' might differ from the native display name.
[2025-07-24 14:32:00.146] Display name '☁️BizyAir Wan_T2V_Pipeline' might differ from the native display name.
[2025-07-24 14:32:00.240] Display name '☁️BizyAir LoraLoader_Legacy' might differ from the native display name.
[2025-07-24 14:32:00.240] Display name '☁️BizyAir ControlNetLoader_Legacy' might differ from the native display name.
[2025-07-24 14:32:00.242] Display name '☁️BizyAir SamplerCustomAdvanced' might differ from the native display name.
[2025-07-24 14:32:00.242] Display name '☁️BizyAir BasicGuider' might differ from the native display name.
[2025-07-24 14:32:00.244] Display name '☁️BizyAir BasicScheduler' might differ from the native display name.
[2025-07-24 14:32:00.244] Display name '☁️BizyAir DualCLIPLoader' might differ from the native display name.
[2025-07-24 14:32:00.245] Display name '☁️BizyAir KSamplerSelect' might differ from the native display name.
[2025-07-24 14:32:00.245] Display name '☁️BizyAir RandomNoise' might differ from the native display name.
[2025-07-24 14:32:00.246] Display name '☁️BizyAir InpaintModelConditioning' might differ from the native display name.
[2025-07-24 14:32:00.247] Display name '☁️BizyAir InpaintModelConditioning_v2' might differ from the native display name.
[2025-07-24 14:32:00.248] Display name '☁️BizyAir ConditioningZeroOut' might differ from the native display name.
[2025-07-24 14:32:00.248] Display name '☁️BizyAir ConditioningSetTimestepRange' might differ from the native display name.
[2025-07-24 14:32:00.249] Display name '☁️BizyAir PassParameter' might differ from the native display name.
[2025-07-24 14:32:07.383] 

[92m[BizyAir][0m Model hosting service initialized.

[2025-07-24 14:32:07.384] 
