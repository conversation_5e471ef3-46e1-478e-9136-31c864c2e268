## ComfyUI-Manager: installing dependencies done.
[2025-07-24 11:07:32.736] ** ComfyUI startup time: 2025-07-24 11:07:32.736
[2025-07-24 11:07:32.738] ** Platform: Windows
[2025-07-24 11:07:32.738] ** Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 11:07:32.738] ** Python executable: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\python.exe
[2025-07-24 11:07:32.739] ** ComfyUI Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 11:07:32.739] ** ComfyUI Base Folder Path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI
[2025-07-24 11:07:32.740] ** User directory: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user
[2025-07-24 11:07:32.741] ** ComfyUI-Manager config path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\config.ini
[2025-07-24 11:07:32.741] ** Log path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\comfyui.log

Prestartup times for custom nodes:
[2025-07-24 11:07:34.495]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 11:07:34.496]    3.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 11:07:34.496]    3.5 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 11:07:34.496] 
[2025-07-24 11:07:36.754] Checkpoint files will always be loaded safely.
[2025-07-24 11:07:36.783] Total VRAM 15694 MB, total RAM 15694 MB
[2025-07-24 11:07:36.783] pytorch version: 2.4.1+cpu
[2025-07-24 11:07:36.785] Set vram state to: DISABLED
[2025-07-24 11:07:36.785] Device: cpu
[2025-07-24 11:07:38.562] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-07-24 11:07:38.600] torchaudio missing, ACE model will be broken
[2025-07-24 11:07:38.607] torchaudio missing, ACE model will be broken
[2025-07-24 11:07:41.258] Python version: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)]
[2025-07-24 11:07:41.258] ComfyUI version: 0.3.45
[2025-07-24 11:07:41.291] ComfyUI frontend version: 1.23.4
[2025-07-24 11:07:41.291] [Prompt Server] web root: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\comfyui_frontend_package\static
[2025-07-24 11:07:42.076] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py", line 4, in <module>
    import torchaudio
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\__init__.py", line 2, in <module>
    from . import _extension  # noqa  # usort: skip
    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\__init__.py", line 38, in <module>
    _load_lib("libtorchaudio")
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\_extension\utils.py", line 60, in _load_lib
    torch.ops.load_library(path)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torch\_ops.py", line 1295, in load_library
    ctypes.CDLL(path)
  File "ctypes\__init__.py", line 379, in __init__
FileNotFoundError: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.

[2025-07-24 11:07:42.077] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_audio.py module for custom nodes: Could not find module 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\torchaudio\lib\libtorchaudio.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
[2025-07-24 11:07:42.599] [92m[BizyAir][0m BizyAir ComfyUI Plugin: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 11:07:42.665] Display name '☁️BizyAir CogView4_6B_Pipe' might differ from the native display name.
[2025-07-24 11:07:42.668] Display name '☁️BizyAir SetUnionControlNetType' might differ from the native display name.
[2025-07-24 11:07:42.669] Display name '☁️BizyAir ControlNetInpaintingAliMamaApply' might differ from the native display name.
[2025-07-24 11:07:42.669] Display name '☁️BizyAir DisableNoise' might differ from the native display name.
[2025-07-24 11:07:42.669] Display name '☁️BizyAir VPScheduler' might differ from the native display name.
[2025-07-24 11:07:42.671] Display name '☁️BizyAir SplitSigmas' might differ from the native display name.
[2025-07-24 11:07:42.671] Display name '☁️BizyAir SplitSigmasDenoise' might differ from the native display name.
[2025-07-24 11:07:42.671] Display name '☁️BizyAir FlipSigmas' might differ from the native display name.
[2025-07-24 11:07:42.672] Display name '☁️BizyAir CFGGuider' might differ from the native display name.
[2025-07-24 11:07:42.674] Display name '☁️BizyAir TrainDatasetAdd' might differ from the native display name.
[2025-07-24 11:07:42.675] Display name '☁️BizyAir CLIPTextEncodeFlux' might differ from the native display name.
[2025-07-24 11:07:42.676] Display name '☁️BizyAir ReferenceLatent' might differ from the native display name.
[2025-07-24 11:07:42.676] Display name '☁️BizyAir FluxGuidance' might differ from the native display name.
[2025-07-24 11:07:42.677] Display name '☁️BizyAir Hy3D_2_1SimpleMeshGen' might differ from the native display name.
[2025-07-24 11:07:42.678] Display name '☁️BizyAir Hy3DExportMesh' might differ from the native display name.
[2025-07-24 11:07:42.679] Display name '☁️BizyAir InstructPixToPixConditioning' might differ from the native display name.
[2025-07-24 11:07:42.682] Display name '☁️BizyAir IPAdapterModelLoader' might differ from the native display name.
[2025-07-24 11:07:42.683] Display name '☁️BizyAir IPAdapterAdvanced' might differ from the native display name.
[2025-07-24 11:07:42.683] Display name '☁️BizyAir IPAdapterStyleComposition' might differ from the native display name.
[2025-07-24 11:07:42.684] Display name '☁️BizyAir JanusModelLoader' might differ from the native display name.
[2025-07-24 11:07:42.684] Display name '☁️BizyAir JanusImageUnderstanding' might differ from the native display name.
[2025-07-24 11:07:42.684] Display name '☁️BizyAir JanusImageGeneration' might differ from the native display name.
[2025-07-24 11:07:42.686] Display name '☁️BizyAir ModelSamplingSD3' might differ from the native display name.
[2025-07-24 11:07:42.686] Display name '☁️BizyAir ModelSamplingFlux' might differ from the native display name.
[2025-07-24 11:07:42.687] Display name '☁️BizyAir NunchakuPulidApply' might differ from the native display name.
[2025-07-24 11:07:42.687] Display name '☁️BizyAir NunchakuPulidLoader' might differ from the native display name.
[2025-07-24 11:07:42.690] Display name '☁️BizyAir TripleCLIPLoader' might differ from the native display name.
[2025-07-24 11:07:42.696] Display name '☁️BizyAir UltimateSDUpscale' might differ from the native display name.
[2025-07-24 11:07:42.698] Display name '☁️BizyAir Wan_LoraLoader' might differ from the native display name.
[2025-07-24 11:07:42.698] Display name '☁️BizyAir Wan_ImageToVideoPipeline' might differ from the native display name.
[2025-07-24 11:07:42.698] Display name '☁️BizyAir Wan_Model_Loader' might differ from the native display name.
[2025-07-24 11:07:42.698] Display name '☁️BizyAir Wan_T2V_Pipeline' might differ from the native display name.
[2025-07-24 11:07:42.705] Display name '☁️BizyAir LoraLoader_Legacy' might differ from the native display name.
[2025-07-24 11:07:42.706] Display name '☁️BizyAir ControlNetLoader_Legacy' might differ from the native display name.
[2025-07-24 11:07:42.706] Display name '☁️BizyAir SamplerCustomAdvanced' might differ from the native display name.
[2025-07-24 11:07:42.706] Display name '☁️BizyAir BasicGuider' might differ from the native display name.
[2025-07-24 11:07:42.706] Display name '☁️BizyAir BasicScheduler' might differ from the native display name.
[2025-07-24 11:07:42.706] Display name '☁️BizyAir DualCLIPLoader' might differ from the native display name.
[2025-07-24 11:07:42.706] Display name '☁️BizyAir KSamplerSelect' might differ from the native display name.
[2025-07-24 11:07:42.708] Display name '☁️BizyAir RandomNoise' might differ from the native display name.
[2025-07-24 11:07:42.708] Display name '☁️BizyAir InpaintModelConditioning' might differ from the native display name.
[2025-07-24 11:07:42.708] Display name '☁️BizyAir InpaintModelConditioning_v2' might differ from the native display name.
[2025-07-24 11:07:42.708] Display name '☁️BizyAir ConditioningZeroOut' might differ from the native display name.
[2025-07-24 11:07:42.709] Display name '☁️BizyAir ConditioningSetTimestepRange' might differ from the native display name.
[2025-07-24 11:07:42.709] Display name '☁️BizyAir PassParameter' might differ from the native display name.
[2025-07-24 11:07:43.340] 

[92m[BizyAir][0m Model hosting service initialized.

[2025-07-24 11:07:43.340] 
[2025-07-24 11:07:46.443] [34m[ComfyUI-Easy-Use] server: [0mv1.3.1 [92mLoaded[0m
[2025-07-24 11:07:46.443] [34m[ComfyUI-Easy-Use] web root: [0mC:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use\web_version/v2 [92mLoaded[0m
[2025-07-24 11:07:46.484] ----------Jake Upgrade Nodes Loaded----------
[2025-07-24 11:07:46.508] ### Loading: ComfyUI-Manager (V3.34.1)
[2025-07-24 11:07:46.509] [ComfyUI-Manager] network_mode: public
[2025-07-24 11:07:46.644] ### ComfyUI Revision: 150 [9a470e07] *DETACHED | Released on '2025-07-21'
[2025-07-24 11:07:46.675] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ckpts path: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\ckpts[0m
[2025-07-24 11:07:46.676] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using symlinks: False[0m
[2025-07-24 11:07:46.677] [36;20m[C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux] | INFO -> Using ort providers: ['CUDAExecutionProvider', 'DirectMLExecutionProvider', 'OpenVINOExecutionProvider', 'ROCMExecutionProvider', 'CPUExecutionProvider', 'CoreMLExecutionProvider'][0m
[2025-07-24 11:07:46.848] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-07-24 11:07:46.850] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-07-24 11:07:46.905] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-07-24 11:07:47.054] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-07-24 11:07:47.135] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
[2025-07-24 11:07:47.582] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux\node_wrappers\dwpose.py:26: UserWarning: DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly
  warnings.warn("DWPose: Onnxruntime not found or doesn't come with acceleration providers, switch to OpenCV with CPU device. DWPose might run very slowly")
[2025-07-24 11:07:47.632] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\nodes.py", line 2124, in load_custom_node
    module_spec.loader.exec_module(module)
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\__init__.py", line 1, in <module>
    from .faceanalysis import NODE_CLASS_MAPPINGS, NODE_DISPLAY_NAME_MAPPINGS
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis\faceanalysis.py", line 16, in <module>
    raise Exception("Please install either dlib or insightface to use this node.")
Exception: Please install either dlib or insightface to use this node.

[2025-07-24 11:07:47.632] Cannot import C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis module for custom nodes: Please install either dlib or insightface to use this node.
[2025-07-24 11:07:48.576] [34mWAS Node Suite: [0mOpenCV Python FFMPEG support is enabled[0m
[2025-07-24 11:07:48.576] [34mWAS Node Suite [93mWarning: [0m`ffmpeg_bin_path` is not set in `C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns\was_suite_config.json` config file. Will attempt to use system ffmpeg binaries if available.[0m
[2025-07-24 11:07:49.294] [34mWAS Node Suite: [0mFinished.[0m [32mLoaded[0m [0m220[0m [32mnodes successfully.[0m
[2025-07-24 11:07:49.294] 
	[3m[93m"Art is the most powerful tool we have to connect with the world and express our individuality."[0m[3m - Unknown[0m
[2025-07-24 11:07:49.294] 
[2025-07-24 11:07:49.300] 
Import times for custom nodes:
[2025-07-24 11:07:49.300]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\websocket_image_save.py
[2025-07-24 11:07:49.300]    0.0 seconds (IMPORT FAILED): C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\ComfyUI_FaceAnalysis
[2025-07-24 11:07:49.300]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_ipadapter_plus
[2025-07-24 11:07:49.307]    0.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-jakeupgrade
[2025-07-24 11:07:49.307]    0.2 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-manager
[2025-07-24 11:07:49.309]    0.8 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\bizyair
[2025-07-24 11:07:49.309]    1.0 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui_controlnet_aux
[2025-07-24 11:07:49.309]    1.7 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\was-ns
[2025-07-24 11:07:49.309]    3.1 seconds: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\custom_nodes\comfyui-easy-use
[2025-07-24 11:07:49.309] 
[2025-07-24 11:07:49.310] WARNING: some comfy_extras/ nodes did not import correctly. This may be because they are missing some dependencies.

[2025-07-24 11:07:49.310] IMPORT FAILED: nodes_audio.py
[2025-07-24 11:07:49.310] 
This issue might be caused by new missing dependencies added the last time you updated ComfyUI.
[2025-07-24 11:07:49.310] Please run the update script: update/update_comfyui.bat
[2025-07-24 11:07:49.310] 
[2025-07-24 11:07:49.682] Context impl SQLiteImpl.
[2025-07-24 11:07:49.683] Will assume non-transactional DDL.
[2025-07-24 11:07:49.684] No target revision found.
[2025-07-24 11:07:49.711] Starting server

[2025-07-24 11:07:49.711] To see the GUI go to: http://127.0.0.1:8188
[2025-07-24 11:07:51.641] C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\bizyengine\core\common\client.py:69: UserWarning: invalid api_key
  warnings.warn("invalid api_key")
[2025-07-24 11:07:52.294] FETCH ComfyRegistry Data: 5/92
[2025-07-24 11:07:57.410] FETCH ComfyRegistry Data: 10/92
[2025-07-24 11:08:02.823] FETCH ComfyRegistry Data: 15/92
[2025-07-24 11:08:08.146] FETCH ComfyRegistry Data: 20/92
[2025-07-24 11:08:13.830] FETCH ComfyRegistry Data: 25/92
[2025-07-24 11:08:19.190] FETCH ComfyRegistry Data: 30/92
[2025-07-24 11:08:24.168] FETCH ComfyRegistry Data: 35/92
[2025-07-24 11:08:28.452] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json [DONE]
[2025-07-24 11:08:30.707] FETCH ComfyRegistry Data: 40/92
[2025-07-24 11:08:37.466] FETCH ComfyRegistry Data: 45/92
[2025-07-24 11:08:42.674] FETCH ComfyRegistry Data: 50/92
[2025-07-24 11:08:48.761] FETCH ComfyRegistry Data: 55/92
[2025-07-24 11:08:49.532] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json [DONE]
[2025-07-24 11:08:49.619] Install model 'XLabs-AI/flux-ip-adapter' from 'https://huggingface.co/XLabs-AI/flux-ip-adapter/resolve/main/ip_adapter.safetensors' into 'C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\models\xlabs/ipadapters\ip_adapter.safetensors'
[2025-07-24 11:08:50.740] Downloading https://cdn-lfs-us-1.hf.co/repos/ab/64/ab6469d53b64b2cc05bfae262dc9c68f3d692eee545dd830ba1141c67a8f8753/750f912149b84bbb0c2a6ce90ffa7e78afd1795821407718724ebcd36372dc2d?response-content-disposition=inline%3B+filename*%3DUTF-8%27%27ip_adapter.safetensors%3B+filename%3D%22ip_adapter.safetensors%22%3B&Expires=1753339017&Policy=eyJTdGF0ZW1lbnQiOlt7IkNvbmRpdGlvbiI6eyJEYXRlTGVzc1RoYW4iOnsiQVdTOkVwb2NoVGltZSI6MTc1MzMzOTAxN319LCJSZXNvdXJjZSI6Imh0dHBzOi8vY2RuLWxmcy11cy0xLmhmLmNvL3JlcG9zL2FiLzY0L2FiNjQ2OWQ1M2I2NGIyY2MwNWJmYWUyNjJkYzljNjhmM2Q2OTJlZWU1NDVkZDgzMGJhMTE0MWM2N2E4Zjg3NTMvNzUwZjkxMjE0OWI4NGJiYjBjMmE2Y2U5MGZmYTdlNzhhZmQxNzk1ODIxNDA3NzE4NzI0ZWJjZDM2MzcyZGMyZD9yZXNwb25zZS1jb250ZW50LWRpc3Bvc2l0aW9uPSoifV19&Signature=Ro1Y0THqKEMlhcxMSK0KYGsvWIdZNuAn0x13wMuVoBTkk3wvcsogoLFHyGlIybKTv57PCLhFD6pTpKKD7VPkB13lhlijQcUqvt4EvR8Y%7EYXB3AaqLfANIUzTpElu2j2ZW8BofG0RIAxxxT1pNAbGbjYlbapCXhm0OePmZ4JaFWdung-RqLvWjB%7EqL88W-7Nu9R7Rga%7Esn5isrA8CSnnKg0chgPduAP8G4ZxTrn7ifEr3HBkxwKas9W7OI69fH7Y3qcy55d6UMUxaJlTUib3FBLbq8KVH4Ny3wcqu4xsEviogmDjtamgtn%7EfzRcQDA2Ews5KK0KQkP1dMVNzUpvV8mg__&Key-Pair-Id=K24J24Z295AEI9 to C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\models\xlabs/ipadapters\ip_adapter.safetensors
[2025-07-24 11:08:54.284] FETCH ComfyRegistry Data: 60/92
[2025-07-24 11:09:00.986] FETCH ComfyRegistry Data: 65/92
[2025-07-24 11:09:07.135] FETCH ComfyRegistry Data: 70/92
[2025-07-24 11:09:13.486] FETCH ComfyRegistry Data: 75/92
[2025-07-24 11:09:19.595] FETCH ComfyRegistry Data: 80/92
[2025-07-24 11:09:26.206] FETCH ComfyRegistry Data: 85/92
[2025-07-24 11:09:32.778] FETCH ComfyRegistry Data: 90/92
[2025-07-24 11:09:35.682] FETCH ComfyRegistry Data [DONE]
[2025-07-24 11:09:35.875] [ComfyUI-Manager] default cache updated: https://api.comfy.org/nodes
[2025-07-24 11:09:35.902] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-24 11:09:37.428] [ComfyUI-Manager] All startup tasks have been completed.
[2025-07-24 11:09:48.859] [ComfyUI-Manager] The ComfyRegistry cache update is still in progress, so an outdated cache is being used.
[2025-07-24 11:09:48.940] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json [DONE]
[2025-07-24 11:09:50.152] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json [DONE]
[2025-07-24 11:09:50.514] FETCH DATA from: C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\user\default\ComfyUI-Manager\cache\832903789_extras.json [DONE]
[2025-07-24 11:09:50.628] FETCH DATA from: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json [DONE]
[2025-07-24 11:10:55.586] 
100%|███████████████████████████▊| 977043456/981760648 [02:04<00:01, 4369462.90it/s]
100%|███████████████████████████▉| 977534976/981760648 [02:04<00:01, 2812721.31it/s]
100%|███████████████████████████▉| 978157568/981760648 [02:04<00:01, 3360069.76it/s]
100%|███████████████████████████▉| 978747392/981760648 [02:04<00:00, 3737645.79it/s]
100%|███████████████████████████▉| 979238912/981760648 [02:05<00:00, 3302711.74it/s]
100%|███████████████████████████▉| 979664896/981760648 [02:05<00:00, 3452520.44it/s]
100%|███████████████████████████▉| 980090880/981760648 [02:05<00:00, 3591566.39it/s]
100%|███████████████████████████▉| 980516864/981760648 [02:05<00:00, 3453696.76it/s]
100%|███████████████████████████▉| 981008384/981760648 [02:05<00:00, 3631436.19it/s]
100%|████████████████████████████| 981760648/981760648 [02:05<00:00, 4500482.72it/s]
100%|████████████████████████████| 981760648/981760648 [02:05<00:00, 7812246.30it/s]
[2025-07-24 11:10:56.948] 
[ComfyUI-Manager] Queued works are completed.
{'install-model': 1}
[2025-07-24 11:10:56.948] 
After restarting ComfyUI, please refresh the browser.
[2025-07-24 11:11:21.778] got prompt
[2025-07-24 11:11:21.791] invalid prompt: {'type': 'invalid_prompt', 'message': 'Cannot execute because a node is missing the class_type property.', 'details': "Node ID '#7'", 'extra_info': {}}
[2025-07-24 11:13:20.486] got prompt
[2025-07-24 11:13:20.486] invalid prompt: {'type': 'invalid_prompt', 'message': 'Cannot execute because a node is missing the class_type property.', 'details': "Node ID '#7'", 'extra_info': {}}
[2025-07-24 11:14:25.651] got prompt
[2025-07-24 11:14:25.654] Failed to validate prompt for output 11:
[2025-07-24 11:14:25.655] * Canny 4:
[2025-07-24 11:14:25.655]   - Value 200.0 bigger than max of 0.99: high_threshold
[2025-07-24 11:14:25.655]   - Value 100.0 bigger than max of 0.99: low_threshold
[2025-07-24 11:14:25.656] Output will be ignored
[2025-07-24 11:14:25.656] invalid prompt: {'type': 'prompt_outputs_failed_validation', 'message': 'Prompt outputs failed validation', 'details': '', 'extra_info': {}}
[2025-07-24 11:14:43.904] got prompt
[2025-07-24 11:14:44.219] model weight dtype torch.float32, manual cast: None
[2025-07-24 11:14:44.244] model_type EPS
[2025-07-24 11:14:46.156] Using split attention in VAE
[2025-07-24 11:14:46.158] Using split attention in VAE
[2025-07-24 11:14:46.442] VAE load device: cpu, offload device: cpu, dtype: torch.float32
[2025-07-24 11:14:46.753] Requested to load SD1ClipModel
[2025-07-24 11:14:46.770] loaded completely 9.5367431640625e+25 235.84423828125 True
[2025-07-24 11:14:46.775] CLIP/text encoder model load device: cpu, offload device: cpu, current: cpu, dtype: torch.float16
[2025-07-24 11:14:47.851] Requested to load AutoencoderKL
[2025-07-24 11:14:47.872] loaded completely 9.5367431640625e+25 319.11416244506836 True
[2025-07-24 11:14:54.684] !!! Exception during processing !!! False not true.
Invalid input thresholds. low_threshold should be smaller than the high_threshold. Got: 0.99>0.9800000000000002
[2025-07-24 11:14:54.771] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_canny.py", line 19, in detect_edge
    output = canny(image.to(comfy.model_management.get_torch_device()).movedim(-1, 1), low_threshold, high_threshold)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\kornia\filters\canny.py", line 76, in canny
    KORNIA_CHECK(
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\kornia\core\check.py", line 123, in KORNIA_CHECK
    raise Exception(f"{condition} not true.\n{msg}")
Exception: False not true.
Invalid input thresholds. low_threshold should be smaller than the high_threshold. Got: 0.99>0.9800000000000002

[2025-07-24 11:14:54.771] Prompt executed in 10.87 seconds
[2025-07-24 11:15:28.559] got prompt
[2025-07-24 11:15:28.579] !!! Exception during processing !!! False not true.
Invalid input thresholds. low_threshold should be smaller than the high_threshold. Got: 0.99>0.9800000000000002
[2025-07-24 11:15:28.581] Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 427, in execute
    output_data, output_ui, has_subgraph, has_pending_tasks = await get_output_data(prompt_id, unique_id, obj, input_data_all, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 270, in get_output_data
    return_values = await _async_map_node_over_list(prompt_id, unique_id, obj, input_data_all, obj.FUNCTION, allow_interrupt=True, execution_block_cb=execution_block_cb, pre_execute_cb=pre_execute_cb)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 244, in _async_map_node_over_list
    await process_inputs(input_dict, i)
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\execution.py", line 232, in process_inputs
    result = f(**inputs)
             ^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\ComfyUI\comfy_extras\nodes_canny.py", line 19, in detect_edge
    output = canny(image.to(comfy.model_management.get_torch_device()).movedim(-1, 1), low_threshold, high_threshold)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\kornia\filters\canny.py", line 76, in canny
    KORNIA_CHECK(
  File "C:\Users\<USER>\Downloads\ComfyUI_windows_portable_nvidia\ComfyUI_windows_portable\python_embeded\Lib\site-packages\kornia\core\check.py", line 123, in KORNIA_CHECK
    raise Exception(f"{condition} not true.\n{msg}")
Exception: False not true.
Invalid input thresholds. low_threshold should be smaller than the high_threshold. Got: 0.99>0.9800000000000002

[2025-07-24 11:15:28.585] Prompt executed in 0.02 seconds
[2025-07-24 11:15:42.638] got prompt
[2025-07-24 11:15:44.966] Requested to load ControlNet
[2025-07-24 11:15:44.966] Requested to load BaseModel
[2025-07-24 11:15:44.981] loaded completely 9.5367431640625e+25 1378.1704711914062 True
[2025-07-24 11:15:45.024] loaded completely 9.5367431640625e+25 3278.812271118164 True
[2025-07-24 11:20:44.181] got prompt
[2025-07-24 11:20:44.181] invalid prompt: {'type': 'invalid_prompt', 'message': 'Cannot execute because a node is missing the class_type property.', 'details': "Node ID '#7'", 'extra_info': {}}
[2025-07-24 11:20:56.077] 
100%|███████████████████████████████████████████████| 20/20 [05:11<00:00, 14.98s/it]
100%|███████████████████████████████████████████████| 20/20 [05:11<00:00, 15.55s/it]
[2025-07-24 11:21:06.013] Prompt executed in 323.37 seconds
