{"last_node_id": 15, "last_link_id": 20, "nodes": [{"id": 1, "type": "CheckpointLoaderSimple", "pos": [50, 100], "size": {"0": 315, "1": 98}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [2, 3], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [4], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["dreamshaper_8.safetensors"]}, {"id": 2, "type": "LoadImage", "pos": [50, 250], "size": {"0": 315, "1": 314}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [5, 6], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["choose file to upload", "image"]}, {"id": 3, "type": "IPAdapterUnifiedLoader", "pos": [400, 100], "size": {"0": 315, "1": 78}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}], "outputs": [{"name": "model", "type": "MODEL", "links": [7], "slot_index": 0}, {"name": "ipadapter", "type": "IPADAPTER", "links": [8], "slot_index": 1}], "properties": {"Node name for S&R": "IPAdapterUnifiedLoader"}, "widgets_values": ["PLUS FACE (portraits)"]}, {"id": 4, "type": "IPAdapterAdvanced", "pos": [750, 100], "size": {"0": 315, "1": 278}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 7}, {"name": "ipadapter", "type": "IPADAPTER", "link": 8}, {"name": "image", "type": "IMAGE", "link": 5}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [9], "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterAdvanced"}, "widgets_values": [1.2, 0.0, 1.0, "linear", 0.0, 1.0, false]}, {"id": 5, "type": "CLIPTextEncode", "pos": [400, 250], "size": {"0": 315, "1": 76}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cartoon style, 3D animation, Pixar style, cute character, soft lighting, high quality"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [400, 350], "size": {"0": 315, "1": 76}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [11], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, bad anatomy, distorted face"]}, {"id": 7, "type": "VAEEncode", "pos": [750, 450], "size": {"0": 210, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 6}, {"name": "vae", "type": "VAE", "link": 4}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}}, {"id": 8, "type": "K<PERSON><PERSON><PERSON>", "pos": [1100, 100], "size": {"0": 315, "1": 262}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 9}, {"name": "positive", "type": "CONDITIONING", "link": 10}, {"name": "negative", "type": "CONDITIONING", "link": 11}, {"name": "latent_image", "type": "LATENT", "link": 12}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [13], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 12, 5.5, "euler_a", "normal", 0.35]}, {"id": 9, "type": "VAEDecode", "pos": [1450, 100], "size": {"0": 210, "1": 46}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 13}, {"name": "vae", "type": "VAE", "link": 14}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [15], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 10, "type": "SaveImage", "pos": [1450, 200], "size": {"0": 315, "1": 270}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 15}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["FACE_PRESERVED_Cartoon"]}, {"id": 11, "type": "VAELoader", "pos": [1100, 400], "size": {"0": 315, "1": 58}, "flags": {}, "order": 10, "mode": 0, "outputs": [{"name": "VAE", "type": "VAE", "links": [14], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["vae-ft-mse-840000-ema-pruned.safetensors"]}], "links": [[1, 1, 0, 3, 0, "MODEL"], [2, 1, 1, 5, 0, "CLIP"], [3, 1, 1, 6, 0, "CLIP"], [4, 1, 2, 7, 1, "VAE"], [5, 2, 0, 4, 2, "IMAGE"], [6, 2, 0, 7, 0, "IMAGE"], [7, 3, 0, 4, 0, "MODEL"], [8, 3, 1, 4, 1, "IPADAPTER"], [9, 4, 0, 8, 0, "MODEL"], [10, 5, 0, 8, 1, "CONDITIONING"], [11, 6, 0, 8, 2, "CONDITIONING"], [12, 7, 0, 8, 3, "LATENT"], [13, 8, 0, 9, 0, "LATENT"], [14, 11, 0, 9, 1, "VAE"], [15, 9, 0, 10, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}