{"last_node_id": 64, "last_link_id": 80, "nodes": [{"id": 37, "type": "BizyAir_CLIPTextEncode", "pos": {"0": -1311.2598876953125, "1": 855.540283203125}, "size": {"0": 347.74420166015625, "1": 495.5002136230469}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 56, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [60], "slot_index": 0, "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["black forest gateau cake spelling out the words \"BizyAir\", tasty, food photography, dynamic shot"], "shape": 1}, {"id": 56, "type": "PreviewImage", "pos": {"0": 130.16998291015625, "1": 862.4693603515625}, "size": {"0": 340.9342041015625, "1": 490.8702087402344}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 68, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}, {"id": 60, "type": "BizyAir_KSamplerSelect", "pos": {"0": -933, "1": 856}, "size": [352.4129999999989, 117.06200000000013], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [75], "slot_index": 0, "shape": 3, "label": "SAMPLER"}], "properties": {"Node name for S&R": "BizyAir_KSamplerSelect"}, "widgets_values": ["euler"], "shape": 1}, {"id": 47, "type": "BizyAir_BasicGuider", "pos": {"0": -929, "1": 1019}, "size": [348.4129999999989, 99.14100000000008], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 59, "slot_index": 0, "label": "model"}, {"name": "conditioning", "type": "BIZYAIR_CONDITIONING", "link": 60, "slot_index": 1, "label": "conditioning"}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [61], "slot_index": 0, "shape": 3, "label": "GUIDER"}], "properties": {"Node name for S&R": "BizyAir_BasicGuider"}, "widgets_values": [], "shape": 1}, {"id": 50, "type": "BizyAir_SamplerCustomAdvanced", "pos": {"0": -551, "1": 855}, "size": [288.8669999999988, 493.4040000000002], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 74, "label": "noise"}, {"name": "guider", "type": "GUIDER", "link": 61, "label": "guider"}, {"name": "sampler", "type": "SAMPLER", "link": 75, "slot_index": 2, "label": "sampler"}, {"name": "sigmas", "type": "SIGMAS", "link": 73, "label": "sigmas"}, {"name": "latent_image", "type": "LATENT", "link": 63, "slot_index": 4, "label": "latent_image"}], "outputs": [{"name": "output", "type": "LATENT", "links": [66], "slot_index": 0, "shape": 3, "label": "output"}, {"name": "denoised_output", "type": "LATENT", "links": null, "shape": 3, "label": "denoised_output"}], "properties": {"Node name for S&R": "BizyAir_SamplerCustomAdvanced"}, "widgets_values": [], "shape": 1}, {"id": 58, "type": "BizyAir_BasicScheduler", "pos": {"0": -924, "1": 1175}, "size": [347.4129999999989, 174.7420000000002], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 72, "slot_index": 0, "label": "model"}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [73], "slot_index": 0, "shape": 3, "label": "SIGMAS"}], "properties": {"Node name for S&R": "BizyAir_BasicScheduler"}, "widgets_values": ["normal", 4, 1], "shape": 1}, {"id": 59, "type": "BizyAir_RandomNoise", "pos": {"0": -1658, "1": 1166}, "size": {"0": 313.49420166015625, "1": 184.03021240234375}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [74], "slot_index": 0, "shape": 3, "label": "NOISE"}], "properties": {"Node name for S&R": "BizyAir_RandomNoise"}, "widgets_values": [438153234912083, "fixed"], "shape": 1}, {"id": 36, "type": "BizyAir_DualCLIPLoader", "pos": {"0": -1662, "1": 988}, "size": {"0": 308.4441833496094, "1": 121.13021087646484}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_CLIP", "type": "BIZYAIR_CLIP", "links": [56], "slot_index": 0, "shape": 3, "label": "BIZYAIR_CLIP"}], "properties": {"Node name for S&R": "BizyAir_DualCLIPLoader"}, "widgets_values": ["t5xxl_fp8_e4m3fn.safetensors", "clip_l.safetensors", "flux"], "shape": 1}, {"id": 48, "type": "BizyAir_UNETLoader", "pos": {"0": -1662, "1": 857}, "size": {"0": 315, "1": 82}, "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_MODEL", "type": "BIZYAIR_MODEL", "links": [59, 72], "slot_index": 0, "shape": 3, "label": "BIZYAIR_MODEL"}], "properties": {"Node name for S&R": "BizyAir_UNETLoader"}, "widgets_values": ["flux/flux1-schnell.sft", "fp8_e4m3fn"], "shape": 1}, {"id": 51, "type": "EmptyLatentImage", "pos": {"0": -933, "1": 667}, "size": [343.09600000000023, 106], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [63], "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1], "shape": 1}, {"id": 54, "type": "BizyAir_VAEDecode", "pos": {"0": -223, "1": 1109}, "size": [307.1967000000011, 242.6365999999996], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 66, "slot_index": 0, "label": "samples"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 67, "slot_index": 1, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [68], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAir_VAEDecode"}, "widgets_values": [], "shape": 1}, {"id": 55, "type": "BizyAir_VAELoader", "pos": {"0": -227, "1": 868}, "size": [316.4454000000012, 196.32339999999976], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "vae", "type": "BIZYAIR_VAE", "links": [67], "shape": 3, "label": "vae"}], "properties": {"Node name for S&R": "BizyAir_VAELoader"}, "widgets_values": ["flux/ae.sft"], "shape": 1}], "links": [[56, 36, 0, 37, 0, "BIZYAIR_CLIP"], [59, 48, 0, 47, 0, "BIZYAIR_MODEL"], [60, 37, 0, 47, 1, "BIZYAIR_CONDITIONING"], [61, 47, 0, 50, 1, "GUIDER"], [63, 51, 0, 50, 4, "LATENT"], [66, 50, 0, 54, 0, "LATENT"], [67, 55, 0, 54, 1, "BIZYAIR_VAE"], [68, 54, 0, 56, 0, "IMAGE"], [72, 48, 0, 58, 0, "BIZYAIR_MODEL"], [73, 58, 0, 50, 3, "SIGMAS"], [74, 59, 0, 50, 0, "NOISE"], [75, 60, 0, 50, 2, "SAMPLER"]], "groups": [{"title": "VAE", "bounding": [-246, 779, 346, 589], "color": "#a1309b", "font_size": 24, "flags": {}}, {"title": "预览图", "bounding": [101, 779, 391, 590], "color": "#444", "font_size": 24, "flags": {}}, {"title": "模型", "bounding": [-1678, 778, 347, 590], "color": "#A88", "font_size": 24, "flags": {}}, {"title": "提示词", "bounding": [-1330, 778, 384, 591], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"title": "采样器", "bounding": [-945, 779, 697, 589], "color": "#8A8", "font_size": 24, "flags": {}}, {"title": "垫图", "bounding": [-946, 597, 371, 180], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.620921323059155, "offset": [1702.5522399999998, -376.55861999999945]}}, "version": 0.4}