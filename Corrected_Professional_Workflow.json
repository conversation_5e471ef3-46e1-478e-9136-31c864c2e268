{"last_node_id": 13, "last_link_id": 18, "nodes": [{"id": 1, "type": "LoadImage", "pos": [50, 50], "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1, 2], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["image"]}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [400, 50], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [3], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [4, 5], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [6, 7], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["DreamShaper_8_pruned.safetensors"]}, {"id": 3, "type": "ControlNetLoader", "pos": [750, 50], "size": {"0": 315, "1": 58}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [8], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["control_v11p_sd15_canny.pth"]}, {"id": 4, "type": "IPAdapterUnifiedLoader", "pos": [1100, 50], "size": {"0": 315, "1": 78}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 3}], "outputs": [{"name": "model", "type": "MODEL", "links": [9], "slot_index": 0}, {"name": "ipadapter", "type": "IPADAPTER", "links": [10], "slot_index": 1}], "properties": {"Node name for S&R": "IPAdapterUnifiedLoader"}, "widgets_values": ["PLUS (high strength)"]}, {"id": 5, "type": "<PERSON><PERSON>", "pos": [50, 400], "size": {"0": 315, "1": 106}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [11], "slot_index": 0}], "properties": {"Node name for S&R": "<PERSON><PERSON>"}, "widgets_values": [100, 200]}, {"id": 6, "type": "ControlNetApply", "pos": [400, 400], "size": {"0": 315, "1": 98}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 12}, {"name": "control_net", "type": "CONTROL_NET", "link": 8}, {"name": "image", "type": "IMAGE", "link": 11}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [13], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetApply"}, "widgets_values": [0.85, 0.0, 1.0]}, {"id": 7, "type": "IPAdapter", "pos": [750, 400], "size": {"0": 315, "1": 200}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 9}, {"name": "ipadapter", "type": "IPADAPTER", "link": 10}, {"name": "image", "type": "IMAGE", "link": 2}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [14], "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapter"}, "widgets_values": [1.0, 0.0, 1.0, "standard"]}, {"id": 8, "type": "CLIPTextEncode", "pos": [1100, 400], "size": {"0": 315, "1": 98}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 4}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["professional cartoon character, high-quality 3D animation style, Pixar Disney quality, detailed facial features, vibrant colors, smooth shading, perfect lighting, masterpiece quality, best quality"]}, {"id": 9, "type": "CLIPTextEncode", "pos": [1450, 400], "size": {"0": 315, "1": 98}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [15], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["realistic photography, photorealistic, low quality, blurry, distorted face, deformed features, bad anatomy, worst quality"]}, {"id": 10, "type": "VAEEncode", "pos": [50, 750], "size": {"0": 315, "1": 46}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 2}, {"name": "vae", "type": "VAE", "link": 6}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [16], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}}, {"id": 11, "type": "K<PERSON><PERSON><PERSON>", "pos": [400, 750], "size": {"0": 315, "1": 262}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 14}, {"name": "positive", "type": "CONDITIONING", "link": 13}, {"name": "negative", "type": "CONDITIONING", "link": 15}, {"name": "latent_image", "type": "LATENT", "link": 16}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [17], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 30, 8.0, "dpmpp_2m", "karras", 0.75]}, {"id": 12, "type": "VAEDecode", "pos": [750, 750], "size": {"0": 315, "1": 46}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 17}, {"name": "vae", "type": "VAE", "link": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [18], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 13, "type": "SaveImage", "pos": [1100, 750], "size": {"0": 315, "1": 270}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 18}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["Professional_Cartoon"]}], "links": [[1, 1, 0, 5, 0, "IMAGE"], [2, 1, 0, 7, 2, "IMAGE"], [3, 2, 0, 4, 0, "MODEL"], [4, 2, 1, 8, 0, "CLIP"], [5, 2, 1, 9, 0, "CLIP"], [6, 2, 2, 10, 1, "VAE"], [7, 2, 2, 12, 1, "VAE"], [8, 3, 0, 6, 1, "CONTROL_NET"], [9, 4, 0, 7, 0, "MODEL"], [10, 4, 1, 7, 1, "IPADAPTER"], [11, 5, 0, 6, 2, "IMAGE"], [12, 8, 0, 6, 0, "CONDITIONING"], [13, 6, 0, 11, 1, "CONDITIONING"], [14, 7, 0, 11, 0, "MODEL"], [15, 9, 0, 11, 2, "CONDITIONING"], [16, 10, 0, 11, 3, "LATENT"], [17, 11, 0, 12, 0, "LATENT"], [18, 12, 0, 13, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}