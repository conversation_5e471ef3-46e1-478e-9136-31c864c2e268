{"last_node_id": 64, "last_link_id": 118, "nodes": [{"id": 64, "type": "Reroute", "pos": {"0": 975, "1": 324, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": [75, 26], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 118}], "outputs": [{"name": "", "type": "BIZYAIR_CLIP", "links": [116, 117], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 61, "type": "PreviewImage", "pos": {"0": 1585, "1": 337, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": [682.3997503871833, 703.249984914559], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 113}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 60, "type": "BizyAir_VAEDecode", "pos": {"0": 1245, "1": 357, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": [316.3603725511821, 46], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 111}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 112}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [113], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BizyAir_VAEDecode"}}, {"id": 56, "type": "BizyAir_CLIPTextEncode", "pos": {"0": 876, "1": 353, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": [335.7290734184928, 152.94001304584947], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 117}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [105], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["\"Create a 2D, 4K image of an SUV with a pure color background, designed in a simplistic manner. The SUV should be painted in a contrasting color against a backdrop of twilight lights, specifically focusing on the bonnet, wheels, and license plate that reads 'BIZYAIR' in stylish, readable fonts. The image should be vibrant yet subtle, emphasizing the essence of a busy air travel with a hint of elegance.\""]}, {"id": 57, "type": "BizyAir_CLIPTextEncode", "pos": {"0": 875, "1": 562, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": [329.4804118213938, 76.00006103515625], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 116}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [106], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["vague"]}, {"id": 55, "type": "BizyAir_TripleCLIPLoader", "pos": {"0": 876, "1": 685, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": [320.05040585147606, 106.65804112721293], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_CLIP", "type": "BIZYAIR_CLIP", "links": [118], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "BizyAir_TripleCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "clip_g.safetensors", "t5xxl_fp16.safetensors"]}, {"id": 54, "type": "BizyAir_CheckpointLoaderSimple", "pos": {"0": 875, "1": 841, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": [323.91249932438575, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "BIZYAIR_MODEL", "links": [109], "shape": 3, "slot_index": 0}, {"name": "clip", "type": "BIZYAIR_CLIP", "links": null, "shape": 3}, {"name": "vae", "type": "BIZYAIR_VAE", "links": [112], "shape": 3}], "properties": {"Node name for S&R": "BizyAir_CheckpointLoaderSimple"}, "widgets_values": ["sd3.5_large_turbo.safetensors"]}, {"id": 58, "type": "EmptySD3LatentImage", "pos": {"0": 1237, "1": 830, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": {"0": 315, "1": 106}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [110], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 59, "type": "BizyAir_KSampler", "pos": {"0": 1247, "1": 485, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "size": [314.1527992344959, 264.1049585550903], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 109}, {"name": "positive", "type": "BIZYAIR_CONDITIONING", "link": 105}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "link": 106}, {"name": "latent_image", "type": "LATENT", "link": 110}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [111], "shape": 3}], "properties": {"Node name for S&R": "BizyAir_KSampler"}, "widgets_values": [147486595332540, "fixed", 4, 1.2, "dpmpp_2m", "sgm_uniform", 1]}], "links": [[105, 56, 0, 59, 1, "BIZYAIR_CONDITIONING"], [106, 57, 0, 59, 2, "BIZYAIR_CONDITIONING"], [109, 54, 0, 59, 0, "BIZYAIR_MODEL"], [110, 58, 0, 59, 3, "LATENT"], [111, 59, 0, 60, 0, "LATENT"], [112, 54, 2, 60, 1, "BIZYAIR_VAE"], [113, 60, 0, 61, 0, "IMAGE"], [116, 64, 0, 57, 0, "BIZYAIR_CLIP"], [117, 64, 0, 56, 0, "BIZYAIR_CLIP"], [118, 55, 0, 64, 0, "*"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.9229599817706556, "offset": [-512.209178148006, 27.813244929989224]}}, "version": 0.4}