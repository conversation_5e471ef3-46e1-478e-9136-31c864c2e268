{"last_node_id": 54, "last_link_id": 59, "nodes": [{"id": 10, "type": "BizyAir_VAELoader", "pos": [73, 123], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "vae", "type": "BIZYAIR_VAE", "links": [27]}], "title": "☁️BizyAir VA<PERSON><PERSON><PERSON>", "properties": {"Node name for S&R": "BizyAir_VAELoader"}, "widgets_values": ["flux/ae.sft"]}, {"id": 11, "type": "BizyAir_DualCLIPLoader", "pos": [72, 238], "size": [315, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_CLIP", "type": "BIZYAIR_CLIP", "links": [25]}], "properties": {"Node name for S&R": "BizyAir_DualCLIPLoader"}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux"]}, {"id": 38, "type": "BizyAir_CLIPVisionLoader", "pos": [80, 405], "size": [327.5999755859375, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP_VISION", "type": "CLIP_VISION", "links": [39, 47]}], "title": "☁️BizyAir CLIPVisionLoader", "properties": {"Node name for S&R": "BizyAir_CLIPVisionLoader"}, "widgets_values": ["sigclip_vision_patch14_384.safetensors"]}, {"id": 42, "type": "BizyAir_StyleModelLoader", "pos": [71, 540], "size": [327.5999755859375, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_STYLE_MODEL", "type": "BIZYAIR_STYLE_MODEL", "links": [52, 57]}], "title": "☁️BizyAir StyleModelLoader", "properties": {"Node name for S&R": "BizyAir_StyleModelLoader"}, "widgets_values": ["flux1-redux-dev.safetensors"]}, {"id": 49, "type": "BizyAir_UNETLoader", "pos": [80, 651], "size": [378, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_MODEL", "type": "BIZYAIR_MODEL", "links": [38]}], "title": "☁️BizyAir UNETLoader", "properties": {"Node name for S&R": "BizyAir_UNETLoader"}, "widgets_values": ["flux/flux1-dev.sft", "default"]}, {"id": 27, "type": "EmptySD3LatentImage", "pos": [1163.************, 1075.************], "size": [315, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [33]}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 26, "type": "BizyAir_FluxGuidance", "pos": [1136.************, 128.44204711914062], "size": [418.1999816894531, 58], "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "conditioning", "type": "BIZYAIR_CONDITIONING", "link": 37}], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [56]}], "properties": {"Node name for S&R": "BizyAir_FluxGuidance"}, "widgets_values": [3.5]}, {"id": 30, "type": "BizyAir_ModelSamplingFlux", "pos": [1141.************, 245.44204711914062], "size": [340.20001220703125, 130], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 38}], "outputs": [{"name": "BIZYAIR_MODEL", "type": "BIZYAIR_MODEL", "links": [34, 35]}], "properties": {"Node name for S&R": "BizyAir_ModelSamplingFlux"}, "widgets_values": [1.1500000000000001, 0.5, 1280, 1280]}, {"id": 50, "type": "BizyAir_LoadImageURL", "pos": [68, 925], "size": [393.5998229980469, 96], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [49, 50], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "BizyAir_LoadImageURL"}, "widgets_values": ["https://bizy-air.oss-cn-beijing.aliyuncs.com/examples_asset/bizyair-instantid-example.webp"]}, {"id": 51, "type": "PreviewImage", "pos": [62, 1069], "size": [210, 246], "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 50}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 46, "type": "LoadImage", "pos": [289, 1022], "size": [210, 314], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [48]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"]}, {"id": 39, "type": "BizyAir_CLIPVisionEncode", "pos": [588, 933], "size": [380.4000244140625, 46], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 39}, {"name": "image", "type": "IMAGE", "link": 49}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [58]}], "title": "☁️BizyAir CLIPVisionEncode", "properties": {"Node name for S&R": "BizyAir_CLIPVisionEncode"}, "widgets_values": []}, {"id": 45, "type": "BizyAir_CLIPVisionEncode", "pos": [596, 1046], "size": [380.4000244140625, 46], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "clip_vision", "type": "CLIP_VISION", "link": 47}, {"name": "image", "type": "IMAGE", "link": 48}], "outputs": [{"name": "CLIP_VISION_OUTPUT", "type": "CLIP_VISION_OUTPUT", "links": [53]}], "title": "☁️BizyAir CLIPVisionEncode", "properties": {"Node name for S&R": "BizyAir_CLIPVisionEncode"}, "widgets_values": []}, {"id": 6, "type": "BizyAir_CLIPTextEncode", "pos": [569, 125], "size": [441, 200], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 25}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [37]}], "title": "☁️BizyAir CLIPTextEncode", "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["a girl face"]}, {"id": 22, "type": "BizyAir_BasicGuider", "pos": [1700, 131], "size": [264.5999755859375, 46], "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 35}, {"name": "conditioning", "type": "BIZYAIR_CONDITIONING", "link": 54}], "outputs": [{"name": "GUIDER", "type": "GUIDER", "links": [30]}], "properties": {"Node name for S&R": "BizyAir_BasicGuider"}, "widgets_values": []}, {"id": 16, "type": "BizyAir_KSamplerSelect", "pos": [1705, 269], "size": [315, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"name": "SAMPLER", "type": "SAMPLER", "links": [31]}], "properties": {"Node name for S&R": "BizyAir_KSamplerSelect"}, "widgets_values": ["euler"]}, {"id": 17, "type": "BizyAir_BasicScheduler", "pos": [1690, 408], "size": [315, 106], "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 34}], "outputs": [{"name": "SIGMAS", "type": "SIGMAS", "links": [32]}], "properties": {"Node name for S&R": "BizyAir_BasicScheduler"}, "widgets_values": ["simple", 20, 1]}, {"id": 13, "type": "BizyAir_SamplerCustomAdvanced", "pos": [1689, 587], "size": [390.5999755859375, 106], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "noise", "type": "NOISE", "link": 29}, {"name": "guider", "type": "GUIDER", "link": 30}, {"name": "sampler", "type": "SAMPLER", "link": 31}, {"name": "sigmas", "type": "SIGMAS", "link": 32}, {"name": "latent_image", "type": "LATENT", "link": 33}], "outputs": [{"name": "output", "type": "LATENT", "links": [26]}, {"name": "denoised_output", "type": "LATENT", "links": null}], "properties": {"Node name for S&R": "BizyAir_SamplerCustomAdvanced"}, "widgets_values": []}, {"id": 8, "type": "BizyAir_VAEDecode", "pos": [1694, 744], "size": [252, 46], "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 26}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 27}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [28]}], "title": "☁️BizyAir VAEDecode", "properties": {"Node name for S&R": "BizyAir_VAEDecode"}, "widgets_values": []}, {"id": 9, "type": "SaveImage", "pos": [1722, 893], "size": [386.3520202636719, 431.4912109375], "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 28}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 25, "type": "BizyAir_RandomNoise", "pos": [1159.************, 926.2467041015625], "size": [315, 82], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"name": "NOISE", "type": "NOISE", "links": [29]}], "properties": {"Node name for S&R": "BizyAir_RandomNoise"}, "widgets_values": [0, "fixed"]}, {"id": 54, "type": "BizyAir_StyleModelApplySimple", "pos": [1134, 421], "size": [493.8000183105469, 98], "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "conditioning", "type": "BIZYAIR_CONDITIONING", "link": 56}, {"name": "style_model", "type": "BIZYAIR_STYLE_MODEL", "link": 57}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 58}], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [59], "slot_index": 0}], "properties": {"Node name for S&R": "BizyAir_StyleModelApplySimple"}, "widgets_values": ["medium"]}, {"id": 53, "type": "BizyAir_StyleModelApplySimple", "pos": [1133, 573], "size": [493.8000183105469, 98], "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "conditioning", "type": "BIZYAIR_CONDITIONING", "link": 59}, {"name": "style_model", "type": "BIZYAIR_STYLE_MODEL", "link": 52}, {"name": "clip_vision_output", "type": "CLIP_VISION_OUTPUT", "link": 53}], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [54], "slot_index": 0}], "properties": {"Node name for S&R": "BizyAir_StyleModelApplySimple"}, "widgets_values": ["medium"]}], "links": [[25, 11, 0, 6, 0, "BIZYAIR_CLIP"], [26, 13, 0, 8, 0, "LATENT"], [27, 10, 0, 8, 1, "BIZYAIR_VAE"], [28, 8, 0, 9, 0, "IMAGE"], [29, 25, 0, 13, 0, "NOISE"], [30, 22, 0, 13, 1, "GUIDER"], [31, 16, 0, 13, 2, "SAMPLER"], [32, 17, 0, 13, 3, "SIGMAS"], [33, 27, 0, 13, 4, "LATENT"], [34, 30, 0, 17, 0, "BIZYAIR_MODEL"], [35, 30, 0, 22, 0, "BIZYAIR_MODEL"], [37, 6, 0, 26, 0, "BIZYAIR_CONDITIONING"], [38, 49, 0, 30, 0, "BIZYAIR_MODEL"], [39, 38, 0, 39, 0, "CLIP_VISION"], [47, 38, 0, 45, 0, "CLIP_VISION"], [48, 46, 0, 45, 1, "IMAGE"], [49, 50, 0, 39, 1, "IMAGE"], [50, 50, 0, 51, 0, "IMAGE"], [52, 42, 0, 53, 1, "BIZYAIR_STYLE_MODEL"], [53, 45, 0, 53, 2, "CLIP_VISION_OUTPUT"], [54, 53, 0, 22, 1, "BIZYAIR_CONDITIONING"], [56, 26, 0, 54, 0, "BIZYAIR_CONDITIONING"], [57, 42, 0, 54, 1, "BIZYAIR_STYLE_MODEL"], [58, 39, 0, 54, 2, "CLIP_VISION_OUTPUT"], [59, 54, 0, 53, 0, "BIZYAIR_CONDITIONING"]], "groups": [{"id": 1, "title": "加载模型", "bounding": [41.21159744262695, 28.03803062438965, 473.5350646972656, 767.98486328125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "加载图片", "bounding": [49.26414489746094, 825.2403564453125, 1015.5521850585938, 499.593505859375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "噪声垫图", "bounding": [1124.1988525390625, 832.9710083007812, 560.663818359375, 454.6601257324219], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "生图采样", "bounding": [1116.2274169921875, 36.89583969116211, 1016.8402099609375, 762.6702270507812], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "提示词", "bounding": [543.0916137695312, 34.251686096191406, 535.897216796875, 763.5427856445312], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5131581182307067, "offset": [-380.3470417116786, -204.10702694005843]}}, "version": 0.4}