# ⚡ Laptop Performance Optimization Guide
*Making ComfyUI Fast on Lower-End Hardware*

## 🐌 **Why Your Laptop is Slow**

### **Current Issues:**
- **30 sampling steps** = 3-5 minutes on laptop
- **CFG 8.0** = High computational load
- **Complex workflow** = IPAdapter + ControlNet = Memory intensive
- **High denoise (0.75)** = More processing needed

### **Typical Laptop Performance:**
- **Integrated GPU**: 5-10 minutes per image
- **Low-end GPU (GTX 1650)**: 2-4 minutes per image
- **Mid-range GPU (RTX 3060)**: 1-2 minutes per image

---

## 🚀 **3 Speed Optimization Levels**

### **Level 1: Moderate Speed (Current Optimized)**
**File**: `Corrected_Professional_Workflow.json` (Updated)
```
Steps: 30 → 15 (50% faster)
CFG: 8.0 → 6.5 (Lighter processing)
Denoise: 0.75 → 0.65 (Less transformation)
IPAdapter: 1.0 → 0.9 (Lighter face processing)
ControlNet: 0.85 → 0.75 (Lighter structure control)
```
**Expected Time**: 1-3 minutes (depending on laptop)

### **Level 2: Ultra Fast (New Workflow)**
**File**: `Ultra_Fast_Laptop_Workflow.json`
```
Steps: 10 (Very fast)
CFG: 5.5 (Minimal processing)
Denoise: 0.6 (Light transformation)
IPAdapter: 0.8 (Basic face preservation)
NO ControlNet (Removes complexity)
Simple prompts (Faster processing)
```
**Expected Time**: 30 seconds - 1 minute

### **Level 3: Lightning Fast (Manual Settings)**
For extremely slow laptops:
```
Steps: 6-8
CFG: 4.5-5.0
Denoise: 0.5
IPAdapter: 0.6
Resolution: 512x512 max
```
**Expected Time**: 15-30 seconds

---

## 🔧 **Additional Speed Optimizations**

### **1. Image Size Optimization**
```
Original: 1024x1024 = Very slow
Optimized: 512x512 = 4x faster
Ultra Fast: 384x384 = 8x faster
```

### **2. Model Optimizations**
```
✅ Use: DreamShaper (Optimized)
❌ Avoid: SDXL models (Too heavy)
❌ Avoid: Multiple ControlNets
❌ Avoid: Complex IPAdapter setups
```

### **3. System Optimizations**
```
✅ Close other applications
✅ Use --lowvram flag if needed
✅ Set Windows to High Performance mode
✅ Ensure adequate cooling
```

---

## 📊 **Performance Comparison**

| Workflow | Steps | Time (Laptop) | Quality | Best For |
|----------|-------|---------------|---------|----------|
| Original | 30 | 3-5 min | Excellent | High-end laptops |
| Optimized | 15 | 1-3 min | Very Good | Mid-range laptops |
| Ultra Fast | 10 | 30s-1min | Good | Low-end laptops |
| Lightning | 6-8 | 15-30s | Basic | Very slow laptops |

---

## 🎯 **Quick Setup Instructions**

### **For Moderate Speed (Recommended):**
1. Use updated `Corrected_Professional_Workflow.json`
2. Load your image
3. Click "Queue Prompt"
4. Wait 1-3 minutes

### **For Ultra Fast:**
1. Load `Ultra_Fast_Laptop_Workflow.json`
2. Load your image
3. Click "Queue Prompt"
4. Wait 30 seconds - 1 minute

### **For Lightning Fast:**
1. Use Ultra Fast workflow
2. Manually change KSampler settings:
   - Steps: 8
   - CFG: 5.0
   - Denoise: 0.5

---

## ⚙️ **Manual Speed Tweaks**

### **If Still Too Slow:**

1. **Reduce Steps Further:**
   ```
   Current: 15 → Try: 10 → Try: 8 → Try: 6
   ```

2. **Lower CFG Scale:**
   ```
   Current: 6.5 → Try: 5.5 → Try: 5.0 → Try: 4.5
   ```

3. **Reduce Denoise:**
   ```
   Current: 0.65 → Try: 0.6 → Try: 0.5 → Try: 0.4
   ```

4. **Lower IPAdapter Strength:**
   ```
   Current: 0.9 → Try: 0.8 → Try: 0.7 → Try: 0.6
   ```

### **If Quality Too Low:**
1. **Increase Steps:** 10 → 12 → 15
2. **Increase CFG:** 5.5 → 6.0 → 6.5
3. **Increase IPAdapter:** 0.8 → 0.9 → 1.0

---

## 🖥️ **Hardware Recommendations**

### **Minimum for Decent Speed:**
- **GPU**: GTX 1660 or better
- **RAM**: 16GB
- **VRAM**: 6GB

### **For Good Performance:**
- **GPU**: RTX 3060 or better
- **RAM**: 32GB
- **VRAM**: 8GB+

### **If Your Laptop is Too Slow:**
Consider using online services like:
- Google Colab (Free with limits)
- RunPod (Paid, fast)
- Vast.ai (Paid, affordable)

---

## 🚨 **Emergency Speed Settings**

If nothing works, try these extreme settings:
```
Steps: 4-6
CFG: 4.0
Denoise: 0.3-0.4
Resolution: 384x384
Remove IPAdapter entirely
Use simple prompts only
```

This will be very fast but basic quality.

---

## 📈 **Expected Results**

With optimizations, you should see:
- **50-80% speed improvement**
- **Acceptable quality** for most uses
- **Consistent performance** without crashes
- **Ability to iterate quickly** for testing

The key is finding the right balance between speed and quality for your specific laptop!
