{"last_node_id": 62, "last_link_id": 32, "nodes": [{"id": 4, "type": "BizyAir_CheckpointLoaderSimple", "pos": [-127, 153], "size": [315, 98], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "BIZYAIR_MODEL", "links": [15]}, {"name": "clip", "type": "BIZYAIR_CLIP", "links": null}, {"name": "vae", "type": "BIZYAIR_VAE", "links": [21, 26]}], "title": "☁️BizyAir CheckpointLoaderSimple", "properties": {"Node name for S&R": "BizyAir_CheckpointLoaderSimple"}, "widgets_values": ["sd3.5_large.safetensors"]}, {"id": 58, "type": "BizyAir_TripleCLIPLoader", "pos": [-150, 495], "size": [327.5999755859375, 106], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_CLIP", "type": "BIZYAIR_CLIP", "links": [19]}], "properties": {"Node name for S&R": "BizyAir_TripleCLIPLoader"}, "widgets_values": ["clip_l.safetensors", "clip_g.safetensors", "t5xxl_fp16.safetensors"]}, {"id": 6, "type": "BizyAir_CLIPTextEncode", "pos": [321, 303], "size": [441, 200], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 19}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [23, 28]}], "title": "☁️BizyAir CLIPTextEncode", "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["a toy duck is swimming the the ocean.", [false, true]]}, {"id": 33, "type": "EmptySD3LatentImage", "pos": [469, 597], "size": [315, 106], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [18]}], "properties": {"Node name for S&R": "EmptySD3LatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 8, "type": "BizyAir_VAEDecode", "pos": [1107, 205], "size": [252, 46], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 20}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 21}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [22]}], "title": "☁️BizyAir VAEDecode", "properties": {"Node name for S&R": "BizyAir_VAEDecode"}, "widgets_values": []}, {"id": 3, "type": "BizyAir_KSampler", "pos": [1091, 369], "size": [315, 262], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 15}, {"name": "positive", "type": "BIZYAIR_CONDITIONING", "link": 16}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "link": 17}, {"name": "latent_image", "type": "LATENT", "link": 18}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [20]}], "properties": {"Node name for S&R": "BizyAir_KSampler"}, "widgets_values": [894415093373890, "fixed", 30, 4, "euler", "simple", 1]}, {"id": 46, "type": "BizyAir_ControlNetLoader", "pos": [534, -177], "size": [390.5999755859375, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "CONTROL_NET", "type": "BIZYAIR_CONTROL_NET", "links": [25]}], "title": "☁️BizyAir ControlNetLoader", "properties": {"Node name for S&R": "BizyAir_ControlNetLoader"}, "widgets_values": ["sd3.5_large_controlnet_blur.safetensors"]}, {"id": 44, "type": "BizyAir_ControlNetApplySD3", "pos": [1013, -191], "size": [441, 186], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "positive", "type": "BIZYAIR_CONDITIONING", "link": 23}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "link": 24}, {"name": "control_net", "type": "BIZYAIR_CONTROL_NET", "link": 25}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 26}, {"name": "image", "type": "IMAGE", "link": 30}], "outputs": [{"name": "positive", "type": "BIZYAIR_CONDITIONING", "links": [16]}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "links": [17]}], "title": "☁️BizyAir ControlNetApplySD3", "properties": {"Node name for S&R": "BizyAir_ControlNetApplySD3"}, "widgets_values": [0.7000000000000001, 0, 1]}, {"id": 50, "type": "BizyAir_ConditioningZeroOut", "pos": [530, 175], "size": [418.1999816894531, 26], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "conditioning", "type": "BIZYAIR_CONDITIONING", "link": 28}], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [24]}], "properties": {"Node name for S&R": "BizyAir_ConditioningZeroOut"}, "widgets_values": []}, {"id": 61, "type": "BizyAir_LoadImageURL", "pos": [129.5195770263672, -69.515380859375], "size": [303.82757568359375, 102.78107452392578], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [31, 32], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "BizyAir_LoadImageURL"}, "widgets_values": ["https://bizy-air.oss-cn-beijing.aliyuncs.com/examples_asset/blur.png\n", [false, true]]}, {"id": 9, "type": "SaveImage", "pos": [1508, -244], "size": [892.0303955078125, 960.7239379882812], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 22}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI"]}, {"id": 59, "type": "BizyAir_Image_Encode", "pos": [128.5195770263672, -186.515380859375], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 31}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [30], "slot_index": 0}], "properties": {"Node name for S&R": "BizyAir_Image_Encode"}, "widgets_values": [true]}, {"id": 62, "type": "PreviewImage", "pos": [-161, -205], "size": [210, 246], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 32}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": []}], "links": [[15, 4, 0, 3, 0, "BIZYAIR_MODEL"], [16, 44, 0, 3, 1, "BIZYAIR_CONDITIONING"], [17, 44, 1, 3, 2, "BIZYAIR_CONDITIONING"], [18, 33, 0, 3, 3, "LATENT"], [19, 58, 0, 6, 0, "BIZYAIR_CLIP"], [20, 3, 0, 8, 0, "LATENT"], [21, 4, 2, 8, 1, "BIZYAIR_VAE"], [22, 8, 0, 9, 0, "IMAGE"], [23, 6, 0, 44, 0, "BIZYAIR_CONDITIONING"], [24, 50, 0, 44, 1, "BIZYAIR_CONDITIONING"], [25, 46, 0, 44, 2, "BIZYAIR_CONTROL_NET"], [26, 4, 2, 44, 3, "BIZYAIR_VAE"], [28, 6, 0, 50, 0, "BIZYAIR_CONDITIONING"], [30, 59, 0, 44, 4, "IMAGE"], [31, 61, 0, 59, 0, "IMAGE"], [32, 61, 0, 62, 0, "IMAGE"]], "groups": [{"id": 1, "title": "SD3.5 txt2img", "bounding": [-187.8519287109375, 79.16500854492188, 1681.8138427734375, 629.4733276367188], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "Apply ControlNet", "bounding": [506.2579040527344, -276.0223693847656, 987.1514282226562, 344.62115478515625], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Load image", "bounding": [-183.9932098388672, -277.2178649902344, 681.9222412109375, 345.7257995605469], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.35049389948139237, "offset": [570.6039225814595, 688.7273484837746]}}, "version": 0.4}