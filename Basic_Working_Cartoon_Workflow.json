{"last_node_id": 11, "last_link_id": 15, "nodes": [{"id": 1, "type": "LoadImage", "pos": [50, 50], "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1, 2], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["image"]}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [400, 50], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [3], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [4, 5], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [6, 7], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["DreamShaper_8_pruned.safetensors"]}, {"id": 3, "type": "ControlNetLoader", "pos": [750, 50], "size": {"0": 315, "1": 58}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "CONTROL_NET", "type": "CONTROL_NET", "links": [8], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetLoader"}, "widgets_values": ["control_v11p_sd15_canny.pth"]}, {"id": 4, "type": "<PERSON><PERSON>", "pos": [50, 400], "size": {"0": 315, "1": 106}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9], "slot_index": 0}], "properties": {"Node name for S&R": "<PERSON><PERSON>"}, "widgets_values": [100, 200]}, {"id": 5, "type": "ControlNetApply", "pos": [400, 400], "size": {"0": 315, "1": 98}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 10}, {"name": "control_net", "type": "CONTROL_NET", "link": 8}, {"name": "image", "type": "IMAGE", "link": 9}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [11], "slot_index": 0}], "properties": {"Node name for S&R": "ControlNetApply"}, "widgets_values": [0.8, 0.0, 1.0]}, {"id": 6, "type": "CLIPTextEncode", "pos": [750, 400], "size": {"0": 315, "1": 98}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 4}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["professional cartoon character, high-quality 3D animation style, Pixar Disney quality, detailed facial features, vibrant colors, smooth shading, perfect lighting, masterpiece quality"]}, {"id": 7, "type": "CLIPTextEncode", "pos": [1100, 400], "size": {"0": 315, "1": 98}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["realistic photography, photorealistic, low quality, blurry, distorted face, deformed features, bad anatomy"]}, {"id": 8, "type": "VAEEncode", "pos": [50, 750], "size": {"0": 315, "1": 46}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 2}, {"name": "vae", "type": "VAE", "link": 6}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [13], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}}, {"id": 9, "type": "K<PERSON><PERSON><PERSON>", "pos": [400, 750], "size": {"0": 315, "1": 262}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 3}, {"name": "positive", "type": "CONDITIONING", "link": 11}, {"name": "negative", "type": "CONDITIONING", "link": 12}, {"name": "latent_image", "type": "LATENT", "link": 13}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [14], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 20, 7.0, "dpmpp_2m", "karras", 0.75]}, {"id": 10, "type": "VAEDecode", "pos": [750, 750], "size": {"0": 315, "1": 46}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 14}, {"name": "vae", "type": "VAE", "link": 7}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [15], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 11, "type": "SaveImage", "pos": [1100, 750], "size": {"0": 315, "1": 270}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 15}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["ComfyUI"]}], "links": [[1, 1, 0, 4, 0, "IMAGE"], [2, 1, 0, 8, 0, "IMAGE"], [3, 2, 0, 9, 0, "MODEL"], [4, 2, 1, 6, 0, "CLIP"], [5, 2, 1, 7, 0, "CLIP"], [6, 2, 2, 8, 1, "VAE"], [7, 2, 2, 10, 1, "VAE"], [8, 3, 0, 5, 1, "CONTROL_NET"], [9, 4, 0, 5, 2, "IMAGE"], [10, 6, 0, 5, 0, "CONDITIONING"], [11, 5, 0, 9, 1, "CONDITIONING"], [12, 7, 0, 9, 2, "CONDITIONING"], [13, 8, 0, 9, 3, "LATENT"], [14, 9, 0, 10, 0, "LATENT"], [15, 10, 0, 11, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}