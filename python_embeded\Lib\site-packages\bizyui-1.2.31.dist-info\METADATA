Metadata-Version: 2.4
Name: bizyui
Version: 1.2.31
Summary: Web resources for [a/BizyAir](https://github.com/siliconflow/BizyAir) which contains Comfy Nodes that can run in any environment.
Author-email: SiliconFlow <<EMAIL>>
Project-URL: Repository, https://github.com/siliconflow/BizyAir
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.10
Description-Content-Type: text/markdown

# BizYair Frontend

# Getting Started

``` bash
git clone https://github.com/siliconflow/bizyair_frontend.git
cd bizyair_frontend 
# 安装依赖
npm install
# prettier 
npm run format 
# eslint 
npx eslint src
# release frontend 
npm run dev
#重新打包前端得到.whl包
npm run build:py
#不重新生成forntend_bizyair.js只重新打包bizyui目录得到.whl包
npm run build:bizyui
```


### Installation and Setup
