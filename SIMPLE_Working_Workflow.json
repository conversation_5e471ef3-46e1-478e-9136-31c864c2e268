{"last_node_id": 8, "last_link_id": 12, "nodes": [{"id": 1, "type": "LoadImage", "pos": [50, 100], "size": {"0": 315, "1": 314}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [1, 9], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.jpg", "image"]}, {"id": 2, "type": "CheckpointLoaderSimple", "pos": [400, 50], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [2], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [3, 4], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [5, 6], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["DreamShaper_8_pruned.safetensors"]}, {"id": 3, "type": "CLIPTextEncode", "pos": [400, 200], "size": {"0": 400, "1": 200}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["cartoon style, 3d cartoon character, pixar style, soft lighting, expressive eyes, detailed face, keep hairstyle, preserve face"]}, {"id": 4, "type": "CLIPTextEncode", "pos": [400, 450], "size": {"0": 400, "1": 200}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 4}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [8], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["low quality, bad anatomy, blurry, distorted face, scary, noise, low-res"]}, {"id": 5, "type": "K<PERSON><PERSON><PERSON>", "pos": [850, 200], "size": {"0": 315, "1": 262}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 2}, {"name": "positive", "type": "CONDITIONING", "link": 7}, {"name": "negative", "type": "CONDITIONING", "link": 8}, {"name": "latent_image", "type": "LATENT", "link": 10}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [11], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 8, 4.5, "dpm_fast", "normal", 0.3]}, {"id": 6, "type": "VAEEncode", "pos": [850, 500], "size": {"0": 210, "1": 46}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 9}, {"name": "vae", "type": "VAE", "link": 5}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}}, {"id": 7, "type": "VAEDecode", "pos": [1200, 200], "size": {"0": 210, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 11}, {"name": "vae", "type": "VAE", "link": 6}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 8, "type": "SaveImage", "pos": [1200, 300], "size": {"0": 315, "1": 270}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 12}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["SIMPLE_Cartoon"]}], "links": [[1, 1, 0, 6, 0, "IMAGE"], [2, 2, 0, 5, 0, "MODEL"], [3, 2, 1, 3, 0, "CLIP"], [4, 2, 1, 4, 0, "CLIP"], [5, 2, 2, 6, 1, "VAE"], [6, 2, 2, 7, 1, "VAE"], [7, 3, 0, 5, 1, "CONDITIONING"], [8, 4, 0, 5, 2, "CONDITIONING"], [9, 1, 0, 6, 0, "IMAGE"], [10, 6, 0, 5, 3, "LATENT"], [11, 5, 0, 7, 0, "LATENT"], [12, 7, 0, 8, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}