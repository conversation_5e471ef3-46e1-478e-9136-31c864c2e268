{"last_node_id": 44, "last_link_id": 56, "nodes": [{"id": 29, "type": "BizyAir_MinusZoneChatGLM3TextEncode", "pos": {"0": 285.1164855957031, "1": 1016.7921142578125}, "size": {"0": 422.229248046875, "1": 256.33843994140625}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [43], "slot_index": 0, "shape": 3, "label": "BIZYAIR_CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_MinusZoneChatGLM3TextEncode"}, "widgets_values": ["一个开朗的女孩，在埃及金字塔旁边"], "shape": 1}, {"id": 37, "type": "BizyAirGenerateLightningImage", "pos": {"0": 287.1164855957031, "1": 680.7921142578125}, "size": {"0": 421.76513671875, "1": 289.2697448730469}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [48, 50, 51], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAirGenerateLightningImage"}, "widgets_values": ["\nA joyful young girl, with a bright, genuine smile, surrounded by a serene garden with colorful flowers and butterflies fluttering around her. She is wearing a casual dress, playing with a fluffy dog, her eyes sparkling with happiness and wonder.", 20, "fixed", 1024, 1024, 1.5, 1], "shape": 1}, {"id": 30, "type": "BizyAir_MinusZoneChatGLM3TextEncode", "pos": {"0": 283.1164855957031, "1": 551.7920532226562}, "size": {"0": 430.76513671875, "1": 82.74213409423828}, "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "BIZYAIR_CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [44], "slot_index": 0, "shape": 3, "label": "BIZYAIR_CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_MinusZoneChatGLM3TextEncode"}, "widgets_values": ["nsfw，脸部阴影，低分辨率，jpeg伪影、模糊、糟糕，黑脸，霓虹灯"], "shape": 1}, {"id": 28, "type": "BizyAir_KSampler", "pos": {"0": 1436, "1": 548}, "size": {"0": 409.5643310546875, "1": 722.8025512695312}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 31, "label": "model"}, {"name": "positive", "type": "BIZYAIR_CONDITIONING", "link": 43, "label": "positive"}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "link": 44, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": 45, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [35], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "BizyAir_KSampler"}, "widgets_values": [13, "increment", 20, 5, "euler", "ddim_uniform", 1], "shape": 1}, {"id": 36, "type": "BizyAirRemoveBackground", "pos": {"0": 768, "1": 549}, "size": [312.06341667298534, 242.0471057508987], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 48, "label": "image"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": [54], "slot_index": 1, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "BizyAirRemoveBackground"}, "widgets_values": [], "shape": 1}, {"id": 27, "type": "BizyAir_VAELoader", "pos": {"0": 761, "1": 920}, "size": [284.3967720729852, 354.2776636508986], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "vae", "type": "BIZYAIR_VAE", "links": [30, 36], "slot_index": 0, "shape": 3, "label": "vae"}], "properties": {"Node name for S&R": "BizyAir_VAELoader"}, "widgets_values": ["sdxl/sdxl_vae.safetensors"], "shape": 1}, {"id": 25, "type": "BizyAir_MZ_KolorsUNETLoaderV2", "pos": {"0": -232, "1": 552}, "size": [466.9653977891862, 723.2855281425987], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "BIZYAIR_MODEL", "links": [31], "slot_index": 0, "shape": 3, "label": "model"}], "properties": {"Node name for S&R": "BizyAir_MZ_KolorsUNETLoaderV2"}, "widgets_values": ["kolors/Kolors-Inpainting.safetensors"], "shape": 1}, {"id": 32, "type": "BizyAir_VAEDecode", "pos": {"0": 1891, "1": 559}, "size": [422.0064507891848, 145.700691142599], "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 35, "label": "samples"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 36, "slot_index": 1, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [53], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAir_VAEDecode"}, "widgets_values": [], "shape": 1}, {"id": 26, "type": "BizyAir_VAEEncodeForInpaint", "pos": {"0": 1070, "1": 910}, "size": [310.79998779296875, 360.1273441117987], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 50, "label": "pixels"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 30, "label": "vae"}, {"name": "mask", "type": "MASK", "link": 55, "label": "mask"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [45], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "BizyAir_VAEEncodeForInpaint"}, "widgets_values": [0], "shape": 1}, {"id": 43, "type": "InvertMask", "pos": {"0": 1106, "1": 553}, "size": [279.2171329729854, 230.6625005508987], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 54, "label": "mask"}], "outputs": [{"name": "MASK", "type": "MASK", "links": [55], "slot_index": 0, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "InvertMask"}, "widgets_values": [], "shape": 1}, {"id": 41, "type": "PreviewImage", "pos": {"0": 2371, "1": 542}, "size": [452.64921975848665, 727.7408880076996], "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 53, "slot_index": 0, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}, {"id": 42, "type": "Note", "pos": {"0": -372, "1": -167}, "size": {"0": 1938.815673828125, "1": 219.7587127685547}, "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["Kolors-Inpainting 模型可以做局部重绘。\n本例子中，使用 BizyAir 的节点生成了一张原始图片，然后使用 BizyAir 的抠图节点得到其背景的 mask。\n然后利用 Kolors Inpainting 模型将背景替换为金字塔。"], "color": "#432", "bgcolor": "#653"}, {"id": 39, "type": "PreviewImage", "pos": {"0": 890, "1": 169}, "size": [353.624389258488, 247.38227180770014], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 51, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}], "links": [[30, 27, 0, 26, 1, "BIZYAIR_VAE"], [31, 25, 0, 28, 0, "BIZYAIR_MODEL"], [35, 28, 0, 32, 0, "LATENT"], [36, 27, 0, 32, 1, "BIZYAIR_VAE"], [43, 29, 0, 28, 1, "BIZYAIR_CONDITIONING"], [44, 30, 0, 28, 2, "BIZYAIR_CONDITIONING"], [45, 26, 0, 28, 3, "LATENT"], [48, 37, 0, 36, 0, "IMAGE"], [50, 37, 0, 26, 0, "IMAGE"], [51, 37, 0, 39, 0, "IMAGE"], [53, 32, 0, 41, 0, "IMAGE"], [54, 36, 1, 43, 0, "MASK"], [55, 43, 0, 26, 2, "MASK"]], "groups": [{"title": "VAE解码", "bounding": [1862, 468, 476, 263], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "预览图", "bounding": [2343, 470, 503, 823], "color": "#444", "font_size": 24, "flags": {}}, {"title": "采样器", "bounding": [1405, 468, 454, 823], "color": "#8A8", "font_size": 24, "flags": {}}, {"title": "模型", "bounding": [-249, 468, 510, 824], "color": "#a1309b", "font_size": 24, "flags": {}}, {"title": "提示词", "bounding": [262, 468, 468, 823], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"title": "VAE编码", "bounding": [735, 817, 669, 477], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "抠图", "bounding": [733, 468, 671, 344], "color": "#A88", "font_size": 24, "flags": {}}, {"title": "中间生成图", "bounding": [864, 90, 409, 340], "color": "#444", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5131581182307071, "offset": [79.15977240151102, 381.21223911229947]}, "workspace_info": {"id": "TKSZ5XMzonRsm7MzObd2l"}}, "version": 0.4}