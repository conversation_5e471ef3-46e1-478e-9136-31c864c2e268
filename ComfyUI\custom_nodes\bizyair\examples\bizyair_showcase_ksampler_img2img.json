{"last_node_id": 53, "last_link_id": 83, "nodes": [{"id": 51, "type": "LoadImage", "pos": {"0": 80.39998626708984, "1": -372.39996337890625}, "size": {"0": 210, "1": 314}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [78], "slot_index": 0, "shape": 3, "label": "IMAGE"}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3, "label": "MASK"}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["example.png", "image"], "shape": 1}, {"id": 32, "type": "BizyAir_CLIPTextEncode", "pos": {"0": 644.00048828125, "1": -203.99990844726562}, "size": {"0": 380.0406188964844, "1": 144.2179412841797}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 82, "slot_index": 0, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [41], "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["blurry, noisy, messy, lowres, jpeg, artifacts, ill, distorted, malformed"], "shape": 1}, {"id": 31, "type": "BizyAir_CLIPTextEncode", "pos": {"0": 645.00048828125, "1": -365.9998779296875}, "size": {"0": 381.0406188964844, "1": 113.21794891357422}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 83, "slot_index": 0, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [77], "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["a happy girl with wings, high quality, detailed, diffuse light"], "shape": 1}, {"id": 53, "type": "Note", "pos": {"0": 229, "1": -523}, "size": {"0": 1511.1678466796875, "1": 58}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["本 workflow 展示了最基础的一种图生图的方法。\n\n1. 加载图片，使用 BizyAir 的 VAE Encode 节点将图片转为 Latent。作为垫图。\n\n2. 将上一步得到的 Latent 作为输入，传递给 BizyAir Ksampler\n\n结合正向、反向提示词，可以得到与垫图相关的输出图像。"], "color": "#432", "bgcolor": "#653"}, {"id": 28, "type": "BizyAir_CheckpointLoaderSimple", "pos": {"0": 328.39984130859375, "1": -369.2001647949219}, "size": [285.59818349492457, 307.8978213726691], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "BIZYAIR_MODEL", "links": [81], "shape": 3, "label": "model"}, {"name": "clip", "type": "BIZYAIR_CLIP", "links": [82, 83], "slot_index": 1, "shape": 3, "label": "clip"}, {"name": "vae", "type": "BIZYAIR_VAE", "links": [44, 80], "slot_index": 2, "shape": 3, "label": "vae"}], "properties": {"Node name for S&R": "BizyAir_CheckpointLoaderSimple"}, "widgets_values": ["sdxl/Juggernaut-XL_v9_RunDiffusionPhoto_v2.safetensors"], "shape": 1}, {"id": 27, "type": "BizyAir_KSampler", "pos": {"0": 1565.251708984375, "1": -348.99993896484375}, "size": {"0": 239.04061889648438, "1": 320.21795654296875}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 81, "label": "model"}, {"name": "positive", "type": "BIZYAIR_CONDITIONING", "link": 77, "slot_index": 1, "label": "positive"}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "link": 41, "slot_index": 2, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": 79, "slot_index": 3, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [45], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "BizyAir_KSampler"}, "widgets_values": [2, "fixed", 20, 8, "euler", "normal", 0.8], "shape": 1}, {"id": 35, "type": "PreviewImage", "pos": {"0": 2247.7998046875, "1": -397.60015869140625}, "size": {"0": 213.16781616210938, "1": 316.26556396484375}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 46, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}, {"id": 34, "type": "BizyAir_VAEDecode", "pos": {"0": 1919, "1": -246}, "size": {"0": 289.0406188964844, "1": 61.21794891357422}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 45, "label": "samples"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 44, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [46], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAir_VAEDecode"}, "widgets_values": [], "shape": 1}, {"id": 52, "type": "BizyAir_VAEEncode", "pos": {"0": 1143, "1": -100}, "size": {"0": 291.0406188964844, "1": 46}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 78, "label": "pixels"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 80, "label": "vae"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [79], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "BizyAir_VAEEncode"}, "widgets_values": [], "shape": 1}], "links": [[41, 32, 0, 27, 2, "BIZYAIR_CONDITIONING"], [44, 28, 2, 34, 1, "BIZYAIR_VAE"], [45, 27, 0, 34, 0, "LATENT"], [46, 34, 0, 35, 0, "IMAGE"], [77, 31, 0, 27, 1, "BIZYAIR_CONDITIONING"], [78, 51, 0, 52, 0, "IMAGE"], [79, 52, 0, 27, 3, "LATENT"], [80, 28, 2, 52, 1, "BIZYAIR_VAE"], [81, 28, 0, 27, 0, "BIZYAIR_MODEL"], [82, 28, 1, 32, 0, "BIZYAIR_CLIP"], [83, 28, 1, 31, 0, "BIZYAIR_CLIP"]], "groups": [{"title": "预览图", "bounding": [2228, -482, 254, 406], "color": "#444", "font_size": 24, "flags": {}}, {"title": "采样器", "bounding": [1548, -421, 272, 406], "color": "#8A8", "font_size": 24, "flags": {}}, {"title": "提示词", "bounding": [628, -449, 413, 406], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"title": "模型", "bounding": [311, -450, 317, 406], "color": "#a1309b", "font_size": 24, "flags": {}}, {"title": "参考图", "bounding": [59, -451, 250, 406], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.5644739300537776, "offset": [-124.77943349492438, 900.3694856273308]}}, "version": 0.4}