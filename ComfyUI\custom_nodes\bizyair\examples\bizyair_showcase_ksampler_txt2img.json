{"last_node_id": 54, "last_link_id": 84, "nodes": [{"id": 32, "type": "BizyAir_CLIPTextEncode", "pos": {"0": 550.4014892578125, "1": -216.39996337890625}, "size": {"0": 382.5166015625, "1": 179.33389282226562}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 82, "slot_index": 0, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [41], "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["blurry, noisy, messy, lowres, jpeg, artifacts, ill, distorted, malformed"], "shape": 1}, {"id": 31, "type": "BizyAir_CLIPTextEncode", "pos": {"0": 549.4014892578125, "1": -370.39996337890625}, "size": {"0": 383.7165832519531, "1": 115.83390045166016}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "BIZYAIR_CLIP", "link": 83, "slot_index": 0, "label": "clip"}], "outputs": [{"name": "CONDITIONING", "type": "BIZYAIR_CONDITIONING", "links": [77], "shape": 3, "label": "CONDITIONING"}], "properties": {"Node name for S&R": "BizyAir_CLIPTextEncode"}, "widgets_values": ["a happy girl with wings, high quality, detailed, diffuse light"], "shape": 1}, {"id": 27, "type": "BizyAir_KSampler", "pos": {"0": 969.4016723632812, "1": -372.59979248046875}, "size": {"0": 288.31658935546875, "1": 328.23388671875}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "BIZYAIR_MODEL", "link": 81, "label": "model"}, {"name": "positive", "type": "BIZYAIR_CONDITIONING", "link": 77, "slot_index": 1, "label": "positive"}, {"name": "negative", "type": "BIZYAIR_CONDITIONING", "link": 41, "slot_index": 2, "label": "negative"}, {"name": "latent_image", "type": "LATENT", "link": 84, "slot_index": 3, "label": "latent_image"}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [45], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "BizyAir_KSampler"}, "widgets_values": [2, "fixed", 20, 8, "euler", "normal", 1], "shape": 1}, {"id": 35, "type": "PreviewImage", "pos": {"0": 1556.399169921875, "1": -370.60003662109375}, "size": {"0": 281.2165832519531, "1": 329.23388671875}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 46, "label": "images"}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage"}, "widgets_values": [], "shape": 1}, {"id": 34, "type": "BizyAir_VAEDecode", "pos": {"0": 1299.0010986328125, "1": -376.0000305175781}, "size": [220.32873802008044, 326.9124647903053], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 45, "label": "samples"}, {"name": "vae", "type": "BIZYAIR_VAE", "link": 44, "label": "vae"}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [46], "slot_index": 0, "shape": 3, "label": "IMAGE"}], "properties": {"Node name for S&R": "BizyAir_VAEDecode"}, "widgets_values": [], "shape": 1}, {"id": 53, "type": "Note", "pos": {"0": 335, "1": -590}, "size": {"0": 1560.056640625, "1": 62.43889617919922}, "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {"text": ""}, "widgets_values": ["本 workflow 展示了最基础的文生图工作流。它对应了 ComfyUI 的默认工作流。\n\n一共包含 7 个节点：\n\n1. BizyAir Load Checkpoint 用于加载模型\n\n2～3. BizyAir CLIP Text Encode 用于编码提示词\n\n4. Empty Latent Image 用于生成一张纯噪声的垫图。在这里，它不是 BizyAir 节点，是 ComfyUI 原生的节点。\n\n5. BizyAir KSampler 用于产生包含图片信息的 Latent 输出。\n\n6. BizyAir VAE Decode 用于将 Latent 转换为图像。\n\n7. Priview Image 节点用于显示图像。"], "color": "#432", "bgcolor": "#653"}, {"id": 54, "type": "EmptyLatentImage", "pos": {"0": 793.199951171875, "1": 105.80000305175781}, "size": {"0": 221.61659240722656, "1": 133.43389892578125}, "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [84], "slot_index": 0, "shape": 3, "label": "LATENT"}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1], "shape": 1}, {"id": 28, "type": "BizyAir_CheckpointLoaderSimple", "pos": {"0": 67.39997863769531, "1": -371.1998291015625}, "size": [451.55685801519826, 315.09426337428965], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "model", "type": "BIZYAIR_MODEL", "links": [81], "shape": 3, "label": "model"}, {"name": "clip", "type": "BIZYAIR_CLIP", "links": [82, 83], "slot_index": 1, "shape": 3, "label": "clip"}, {"name": "vae", "type": "BIZYAIR_VAE", "links": [44], "slot_index": 2, "shape": 3, "label": "vae"}], "properties": {"Node name for S&R": "BizyAir_CheckpointLoaderSimple"}, "widgets_values": ["sdxl/Juggernaut-XL_v9_RunDiffusionPhoto_v2.safetensors"], "shape": 1}], "links": [[41, 32, 0, 27, 2, "BIZYAIR_CONDITIONING"], [44, 28, 2, 34, 1, "BIZYAIR_VAE"], [45, 27, 0, 34, 0, "LATENT"], [46, 34, 0, 35, 0, "IMAGE"], [77, 31, 0, 27, 1, "BIZYAIR_CONDITIONING"], [81, 28, 0, 27, 0, "BIZYAIR_MODEL"], [82, 28, 1, 32, 0, "BIZYAIR_CLIP"], [83, 28, 1, 31, 0, "BIZYAIR_CLIP"], [84, 54, 0, 27, 3, "LATENT"]], "groups": [{"title": "垫图", "bounding": [770, 37, 270, 210], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "预览图", "bounding": [1536, -452, 319, 422], "color": "#444", "font_size": 24, "flags": {}}, {"title": "提示词", "bounding": [536, -450, 410, 421], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"title": "VAE", "bounding": [1282, -451, 252, 420], "color": "#3f789e", "font_size": 24, "flags": {}}, {"title": "模型", "bounding": [44, -450, 491, 422], "color": "#a1309b", "font_size": 24, "flags": {}}, {"title": "采样器", "bounding": [947, -451, 333, 421], "color": "#8A8", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.751314800901578, "offset": [-37.134836652893696, 708.2955657272726]}}, "version": 0.4}