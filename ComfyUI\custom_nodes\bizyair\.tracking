.flake8
.github/ISSUE_TEMPLATE/bizyair_nodes_feature_request.md
.github/ISSUE_TEMPLATE/bug_report.md
.github/workflows/publish_to_comfy_cli.yml
.github/workflows/python-format.yml
.github/workflows/semver.yml
.gitignore
.gitmodules
.pre-commit-config.yaml
LICENSE
README.md
__init__.py
api_key.ini.example
dev_requirements.txt
examples/bizyair-flux-fill1-inpaint.json
examples/bizyair-flux1-tools-canny.json
examples/bizyair-flux1-tools-depth.json
examples/bizyair-flux1-tools-redux.json
examples/bizyair-flux1-upscale.json
examples/bizyair-shuttleai_shuttle_3_1_aesthetic.json
examples/bizyair_box_point_guided_segment-anything.json
examples/bizyair_controlnet_preprocessor_workflow.json
examples/bizyair_controlnet_union_workflow.json
examples/bizyair_flux_detail_daemon_sampler.json
examples/bizyair_flux_dev_workflow.json
examples/bizyair_flux_img2img_workflow.json
examples/bizyair_flux_joycaption_img2img_workflow.json
examples/bizyair_flux_pixelwave_txt2img.json
examples/bizyair_flux_pulid.json
examples/bizyair_flux_schnell_workflow.json
examples/bizyair_flux_simple_lora_workflow.json
examples/bizyair_generate_photorealistic_images_workflow.json
examples/bizyair_kolors_controlnet.json
examples/bizyair_kolors_inpainting.json
examples/bizyair_kolors_ipa.json
examples/bizyair_kolors_txt2img.json
examples/bizyair_remove_background_workflow.json
examples/bizyair_sd3_5_blur.json
examples/bizyair_sd3_5_canny.json
examples/bizyair_sd3_5_depth.json
examples/bizyair_sd3_5_turbo_txt2img.json
examples/bizyair_sd3_5_txt2img.json
examples/bizyair_sdxl_InstantID_basic.json
examples/bizyair_segment_anything_ultra.json
examples/bizyair_showcase_interior_design.json
examples/bizyair_showcase_ksampler_controlnet.json
examples/bizyair_showcase_ksampler_img2img.json
examples/bizyair_showcase_ksampler_ipadapter.json
examples/bizyair_showcase_ksampler_lora.json
examples/bizyair_showcase_ksampler_txt2img.json
examples/bizyair_showcase_remove_background.json
examples/bizyair_showcase_shark_submarine.json
examples/bizyair_text_guided_segment-anything.json
examples/bizyair_trellis_multi.json
examples/bizyair_trellis_single.json
examples/bizyair_ultimate_sd_upscale.json
increment_version.py
prestartup_script.py
profile.ini.example
pyproject.toml
pytest.ini
requirements.txt
tests-units/README.md
tests-units/bizyair/test_image_utils.py
tests-units/bizyair/test_path_utils.py
tests-units/requirements.txt
tools/convert_to_bizyair.py