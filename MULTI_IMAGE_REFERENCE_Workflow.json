{"last_node_id": 20, "last_link_id": 35, "nodes": [{"id": 1, "type": "CheckpointLoaderSimple", "pos": [50, 100], "size": {"0": 315, "1": 98}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [2, 3], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [4, 5], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["dreamshaper_8.safetensors"]}, {"id": 2, "type": "LoadImage", "pos": [50, 250], "size": {"0": 315, "1": 314}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [6, 7], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["Main_Reference_Image.jpg", "image"]}, {"id": 3, "type": "LoadImage", "pos": [50, 600], "size": {"0": 315, "1": 314}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [8], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["Reference_Image_2.jpg", "image"]}, {"id": 4, "type": "LoadImage", "pos": [50, 950], "size": {"0": 315, "1": 314}, "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["Reference_Image_3.jpg", "image"]}, {"id": 5, "type": "LoadImage", "pos": [50, 1300], "size": {"0": 315, "1": 314}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [10], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": null, "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["Reference_Image_4.jpg", "image"]}, {"id": 6, "type": "IPAdapterUnifiedLoaderFaceID", "pos": [400, 100], "size": {"0": 315, "1": 118}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}], "outputs": [{"name": "model", "type": "MODEL", "links": [11], "slot_index": 0}, {"name": "ipadapter", "type": "IPADAPTER", "links": [12], "slot_index": 1}], "properties": {"Node name for S&R": "IPAdapterUnifiedLoaderFaceID"}, "widgets_values": ["FACEID PLUS V2", 0.8, "CPU"]}, {"id": 7, "type": "IPAdapterFaceIDBatch", "pos": [750, 100], "size": {"0": 315, "1": 358}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 11}, {"name": "ipadapter", "type": "IPADAPTER", "link": 12}, {"name": "image", "type": "IMAGE", "link": 6}, {"name": "image_2", "type": "IMAGE", "link": 8}, {"name": "image_3", "type": "IMAGE", "link": 9}, {"name": "image_4", "type": "IMAGE", "link": 10}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [13], "slot_index": 0}], "properties": {"Node name for S&R": "IPAdapterFaceIDBatch"}, "widgets_values": [1.2, 1.8, "linear", 0.0, 1.0, "concat", false]}, {"id": 8, "type": "CLIPTextEncode", "pos": [400, 300], "size": {"0": 315, "1": 76}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [14], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["same person, 3D cartoon style, Pixar animation, preserve all facial features, detailed eyes, smooth skin, consistent character, high quality render"]}, {"id": 9, "type": "CLIPTextEncode", "pos": [400, 400], "size": {"0": 315, "1": 76}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 3}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [15], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, bad anatomy, distorted face, different person, inconsistent features, realistic photo"]}, {"id": 10, "type": "VAEEncode", "pos": [400, 500], "size": {"0": 210, "1": 46}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 7}, {"name": "vae", "type": "VAE", "link": 4}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [16], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}}, {"id": 11, "type": "K<PERSON><PERSON><PERSON>", "pos": [1100, 100], "size": {"0": 315, "1": 262}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 13}, {"name": "positive", "type": "CONDITIONING", "link": 14}, {"name": "negative", "type": "CONDITIONING", "link": 15}, {"name": "latent_image", "type": "LATENT", "link": 16}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [17], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [12345, "randomize", 12, 6.0, "euler_a", "normal", 0.35]}, {"id": 12, "type": "VAEDecode", "pos": [1450, 100], "size": {"0": 210, "1": 46}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 17}, {"name": "vae", "type": "VAE", "link": 5}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [18], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 13, "type": "SaveImage", "pos": [1450, 200], "size": {"0": 315, "1": 270}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 18}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["Multi_Reference_Perfect_Result"]}], "links": [[1, 1, 0, 6, 0, "MODEL"], [2, 1, 1, 8, 0, "CLIP"], [3, 1, 1, 9, 0, "CLIP"], [4, 1, 2, 10, 1, "VAE"], [5, 1, 2, 12, 1, "VAE"], [6, 2, 0, 7, 2, "IMAGE"], [7, 2, 0, 10, 0, "IMAGE"], [8, 3, 0, 7, 3, "IMAGE"], [9, 4, 0, 7, 4, "IMAGE"], [10, 5, 0, 7, 5, "IMAGE"], [11, 6, 0, 7, 0, "MODEL"], [12, 6, 1, 7, 1, "IPADAPTER"], [13, 7, 0, 11, 0, "MODEL"], [14, 8, 0, 11, 1, "CONDITIONING"], [15, 9, 0, 11, 2, "CONDITIONING"], [16, 10, 0, 11, 3, "LATENT"], [17, 11, 0, 12, 0, "LATENT"], [18, 12, 0, 13, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}